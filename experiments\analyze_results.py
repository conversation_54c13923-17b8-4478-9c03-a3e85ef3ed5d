import json
import os
import sys
import matplotlib
matplotlib.use('Agg') # 使用非交互式后端，防止在无GUI环境报错

# 将项目根目录添加到sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.visualizer import plot_metrics

def analyze_logs(log_path, output_dir):
    """
    读取日志文件，提取指标，并调用可视化函数。
    """
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            logs = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"无法读取或解析日志文件: {log_path}")
        print(f"错误: {e}")
        return

    # history字典动态构建
    history = {}
    
    for log_entry in logs:
        metrics = log_entry.get('global_model_metrics', {})
        for k, v in metrics.items():
            if isinstance(v, (int, float)): # 只记录数值型指标
                if k not in history:
                    history[k] = []
                history[k].append(v)
    
    if not history:
        print("日志中未找到可供分析的指标。")
        return

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 定义图表保存路径
    save_path = os.path.join(output_dir, 'training_metrics_analysis.png')
    
    # 调用绘图函数
    print(f"从 {len(logs)} 轮日志中提取了指标，正在生成图表...")
    plot_metrics(history, save_path=save_path)
    print(f"分析图表已成功保存至: {save_path}")

if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser(description="Analyze training logs and generate plots.")
    parser.add_argument('--log_file', type=str, default='logs/round_logs.json', help='Path to the round logs JSON file.')
    parser.add_argument('--output_dir', type=str, default='outputs/plots', help='Directory to save the plot.')
    
    args = parser.parse_args()
    
    analyze_logs(args.log_file, args.output_dir) 