{"round": 1, "timestamp": "2025-06-09 22:07:32", "global_model_version": "v0", "num_train_clients": 14, "num_committee_clients": 6, "committee_clients_ids": [0, 1, 2, 3, 4, 5], "train_clients_ids": [14, 28, 23, 20, 9, 7, 26, 22, 6, 29, 25, 11, 12, 15], "idle_clients_ids": [8, 10, 13, 16, 17, 18, 19, 21, 24, 27], "download_size_kb": {"14": 313.22265625, "28": 313.22265625, "23": 313.22265625, "20": 313.22265625, "9": 313.22265625, "7": 313.22265625, "26": 313.22265625, "22": 313.22265625, "6": 313.22265625, "29": 313.22265625, "25": 313.22265625, "11": 313.22265625, "12": 313.22265625, "15": 313.22265625}, "selected_committee_ids": {"14": [5, 0, 4, 2], "28": [2, 1, 4, 0], "23": [5, 0, 4, 2], "20": [3, 0, 4, 5], "9": [1, 5, 0, 2], "7": [1, 4, 3, 0], "26": [3, 4, 2, 0], "22": [1, 3, 2, 5], "6": [1, 5, 2, 0], "29": [0, 3, 5, 1], "25": [2, 4, 5, 0], "11": [5, 3, 0, 1], "12": [0, 4, 2, 3], "15": [4, 2, 1, 5]}, "votes": {"14": [true, true, true, true], "28": [true, true, true, true], "23": [true, true, true, true], "20": [true, true, true, true], "9": [true, true, true, true], "7": [true, true, true, true], "26": [true, true, true, true], "22": [true, true, true, true], "6": [true, true, true, true], "29": [true, true, true, true], "25": [true, true, true, true], "11": [true, true, true, true], "12": [true, true, true, true], "15": [true, true, true, true]}, "train_client_updates_validated": {"14": true, "28": true, "23": true, "20": true, "9": true, "7": true, "26": true, "22": true, "6": true, "29": true, "25": true, "11": true, "12": true, "15": true}, "accepted_updates": 14, "reputation_changes": {"0": 0.0, "1": 0.0, "2": 0.0, "3": 0.0, "4": 0.0, "5": 0.0}, "committee_agreement_rate": 1.0, "aggregation_strategy": "credit", "global_model_metrics": {"loss": 0.7021738974671615, "accuracy": 0.5074232926426921, "f1": 0.673232654847888, "precision": 0.5074232926426921, "recall": 1.0, "auc_roc": 0.6495716464436987, "best_threshold": 0.1, "confusion_matrix": {"TP": 1538, "FP": 1493, "FN": 0, "TN": 0}, "global_f1_delta": 0.0}, "rollback": false, "reputation_distribution": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "avg_reputation_score": 0.0, "model_config": {"client_model_type": "huawei-noah/TinyBERT_General_4L_312D", "server_model_type": "huawei-noah/TinyBERT_General_4L_312D", "use_committee_mechanism": true, "use_reputation_mechanism": true, "agg_method": "credit"}}