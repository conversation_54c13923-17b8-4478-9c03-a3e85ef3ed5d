import numpy as np
import random

def select_by_credit(clients, num_committee, num_train):
    """
    按信誉值排序，前m名为委员会，剩余随机选n个为训练客户端。
    """
    sorted_clients = sorted(clients, key=lambda c: c.reputation, reverse=True)
    committee = sorted_clients[:num_committee]
    candidates = sorted_clients[num_committee:]
    train_clients = random.sample(candidates, min(num_train, len(candidates)))
    idle_clients = [c for c in clients if c not in committee and c not in train_clients]
    return committee, train_clients, idle_clients

def select_random(clients, num_committee, num_train):
    """
    随机选择委员会和训练客户端。
    """
    shuffled = clients[:]
    random.shuffle(shuffled)
    committee = shuffled[:num_committee]
    rest = shuffled[num_committee:]
    train_clients = random.sample(rest, min(num_train, len(rest)))
    idle_clients = [c for c in clients if c not in committee and c not in train_clients]
    return committee, train_clients, idle_clients

def roulette_selection(clients, num_select):
    """
    基于信誉分的权重轮盘赌随机选择。
    """
    credits = np.array([max(1e-3, c.reputation) for c in clients])
    probs = credits / credits.sum()
    selected = np.random.choice(clients, size=min(num_select, len(clients)), replace=False, p=probs)
    return list(selected)

def select_by_credit_with_history(clients, last_committee_ids, last_train_ids, last_idle_ids, num_committee, num_train):
    # 1. 上一轮空闲+训练客户端
    candidate_ids = set(last_train_ids) | set(last_idle_ids)
    candidate_clients = [c for c in clients if c.client_id in candidate_ids]
    # 按信誉排序，前m名为新一轮委员会
    sorted_candidates = sorted(candidate_clients, key=lambda c: c.reputation, reverse=True)
    committee = sorted_candidates[:num_committee]
    committee_ids = set(c.client_id for c in committee)
    # 2. 剩余的空闲客户端+上一轮委员会客户端，作为训练客户端候选池
    rest_ids = (set(last_committee_ids) | (candidate_ids - committee_ids))
    rest_clients = [c for c in clients if c.client_id in rest_ids and c.client_id not in committee_ids]
    # 基于信誉分权重轮盘赌采样n个为训练客户端
    if rest_clients:
        credits = np.array([max(1e-3, c.reputation) for c in rest_clients])
        probs = credits / credits.sum()
        train_clients = list(np.random.choice(rest_clients, size=min(num_train, len(rest_clients)), replace=False, p=probs))
    else:
        train_clients = []
    train_ids = set(c.client_id for c in train_clients)
    # 3. 剩下的为本轮空闲
    idle_clients = [c for c in clients if c.client_id not in committee_ids and c.client_id not in train_ids]
    return committee, train_clients, idle_clients 