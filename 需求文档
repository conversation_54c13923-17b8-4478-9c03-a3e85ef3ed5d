# 基于委员会共识和激励机制的联邦学习文本欺诈检测

系统角色划分
客户端类型	功能说明
训练客户端（Training Clients）	下载模型，基于本地对话训练，上传模型参数更新
委员会客户端（Committee Clients）	验证训练客户端上传的模型参数，参与投票共识决定是否采纳。
空闲客户端（Idle Clients）	当前轮未被选中参与训练或验证，作为下一轮候选资源。

  +---------------------------------------------------+
  |                      中央服务器                    |
  +---------------------------------------------------+
         ↓                                 ↓ ↑
    训练客户端 →提交LoRA参数（验证/共识） → 委员会客户端
          ↑            
     （训练/上传）   
          ↑                                 ↑
                 空闲客户端（激活/选择）

详细方法设计

    检测模型设计与选择
        中央服务器使用 TinyBERT + LoRA，训练/委员会客户端使用 TinyBERT + LoRA，仅初始化 LoRA adapter 层
        中央服务器只下发最新一轮的 LoRA adapter 参数；客户端加载到相同结构的 TinyBERT 上，仅更新 LoRA 层
        通过对话文本，预测是否为欺诈，只训练 LoRA 层的参数；训练完成后上传 LoRA adapter 的权重

    激励机制设计
        每个客户端维护一个信誉值（Credit Score），更新规则如下：
            若训练客户端被委员会接受，则训练方声誉 +1，委员会成员 +0.5；
            若训练上传失败，训练方 -1，委员会中投反对票者 +0.5，投赞成票但多数为否者 -0.5。
            三轮内担任三种角色	+0.5
            连续两轮空闲	-0.2（轻微）
            连续两轮委员会	无额外奖励
            每轮结束后，对非参与客户端进行信誉衰减：credit[client] *= 0.98  # 衰减系数
            设客户端 i 的本轮信誉值更新为：crediti(t+1)=min(max(α⋅log(1+si)−β⋅log(1+pi),−5),+5)
                ● si：i 被采纳、投票准确、表现良好次数；
                ● pip_ipi：i 被拒绝、投票偏差、表现差次数；
                ● α、β：权重调节（建议 α=1.0, β=1.2）
                ● 范围 [-5, +5]，防止过度声誉失衡。

    角色选取设计
        每一轮开始时，所有上一轮的空闲客户端和训练客户端按照当前信誉值进行排序：前m名作为当轮的委员会客户端。
        剩余的空闲客户端加上上一轮的委员会客户端随机选择n个作为当轮的训练客户端 （基于权重轮盘赌随机策略）。
        剩下的客户端在当轮保持空闲


    联邦训练流程
            本地模拟联邦学习框架
            按照模拟空闲客户端的个数随机平均划分本地训练集对话数据，委员会客户端数量随机平均划分验证集

                委员会客户端随机平均分配验证集
                训练和空闲客户端随机平均分配训练集
                中央服务器则是使用测试集
                数据集在联邦学习开始时分配后就随客户端固定下来，而不是每一轮都重新划分

        每一轮训练流程如下：
            （1）模型下发
                服务器向训练客户端与委员会客户端下发最新一轮的 LoRA adapter 参数。客户端加载到相同结构的 TinyBERT 上，仅更新 LoRA 层
            （2）训练客户端本地更新
                训练客户端按照串行顺序对被划分到的训练集执行若干轮训练；
                只训练 LoRA 层的参数，训练完成后上传 LoRA adapter 的权重；
                提交给委员会客户端，随机选取k个委员会验证。
            （3）隐私增强的委员会客户端验证
                委员会客户端只用验证集进行验证，不做模型的反向传播
                加载训练客户端的 LoRA 参数合并到 TinyBERT 上，进行一次前向推理验证性能
                    验证标准包括：
                        本地验证集评估指标（如 f1_rollback_threshold 是否提升）；（上一轮中央服务器聚合的后下发的模型参数下发至训练、委员会客户端，委员会客户端在进行评估时，使用上一轮中央服务器聚合后的模型参数在所分配到的验证集上进行评估获得f1分数基准值，收到训练客户端的模型参数后，使用训练客户端的模型参数在验证集上进行评估获得f1分数与f1分数基准值进行比较，获得f1分数提升值）
                        与全局模型差异度（如 cosine_distance_threshold）；
                        使用共识机制决定是否接受更新（如超过2/3通过即为有效）；
                    分阶段动态评分-阈值机制
                        将 F1 提升权重、余弦相似度权重 和 阈值 三者绑定，根据训练轮次动态调整，形成渐进严格的筛查逻辑。
                            阶段划分与参数联动表
                            训练阶段	轮次范围	F1权重 (α)	余弦权重 (β)	综合阈值 (θ)	余弦独立阈值 (γ)	设计目标
                            探索期	1-10	0.7	0.3	0.4	0.35	鼓励性能提升，容忍参数差异
                            过渡期	11-20	0.5	0.5	0.5	0.3	平衡性能与稳定性
                            稳定期	21-30	0.3	0.7	0.6	0.25	严格参数一致性，抑制噪声
                            收敛期  30以后	0.1	0.8	0.65 0.2
                        筛查条件变为 双轨制：满足 综合评分达标 或 独立余弦条件达标
                                        True,if (α⋅f1_rollback_threshold+β⋅cosine_sim)≥θ
                            通过条件=   True,if cosine_sim≥γ
                                        False,otherwise
                通过者上传至中央服务器。
            （4）聚合与更新
                中央服务器收集所有通过验证的 LoRA 参数；对每个 adapter 权重进行加权平均；更新为新的全局 LoRA adapter，供下一轮下发
                服务器聚合后可使用本地测试集进行一次模型评估

    回滚机制
        允许的F1最大下降幅度，超过则回滚
        增大Cosine距离阈值，更宽松
        最少需要3个模型才进行聚合，否则保持上一轮模型
    
    参数搜集
        一、通用训练日志字段（所有实验通用）
        字段名	含义	示例	说明
        round	当前训练轮数	5	便于横向对比轮数性能变化
        timestamp	时间戳	2025-05-26 15:42:00	可选，用于运行耗时分析
        global_model_version	聚合后模型版本号	v5	记录每轮模型标识
        num_train_clients	本轮训练客户端数	10	可对比客户端数量对性能的影响
        num_committee_clients	本轮委员会客户端数	5	用于分析共识机制表现
        accepted_updates	被委员会接受的模型数	8	可用于评估训练质量

        二、客户端相关日志
            字段名	含义	示例	说明
            train_clients_ids	参与训练的客户端编号	[1, 3, 7, 10, 15]	可复现实验
            committee_clients_ids	当前委员会成员	[2, 4, 8, 9, 11]	
            train_client_updates_validated	每个训练客户端是否被委员会接受	{1: True, 3: False, ...}	用于后续错误率分析
            train_client_scores	当前各训练客户端训练后的 F1 / Accuracy	{1: 0.91, 3: 0.84, ...}	可横向比较
            upload_size_kb  上传参数大小（单位：kb）
            download_size_kb    下载模型参数大小
            local_train_time_sec    本地训练耗时（秒）
            

        三、聚合后评估指标（服务端测试集）
            字段名	含义	示例	说明
            test_accuracy	聚合后模型准确率	0.927	常规指标
            test_precision	精准率	0.912	
            test_recall	召回率	0.945	
            test_f1_score	F1综合指标	0.928	主要比较指标
            global_f1_delta   当前轮和上一轮的test_f1_score差值
            test_auc_roc	AUC-ROC 值	0.974	非常重要的诈骗检测指标
            test_loss	CrossEntropy 或 BCE Loss	0.183	趋势图判断收敛
            test_confusion_matrix	混淆矩阵	[TP=87, FP=5, FN=6, TN=102]	可视化或异常追踪使用

        四、共识机制与激励机制指标（消融对比用）
            字段名	含义	示例	说明
            committee_agreement_rate	投票一致比例	0.86	表示是否共识稳定（2/3 投票是否容易达成）
            reputation_changes	每个客户端本轮信誉变化	{1: +1, 2: +0.5, 3: -0.5}	用于激励机制效果分析
            avg_reputation_score	当前所有客户端信誉均值	5.2	可观察声誉系统是否偏移
            reputation_distribution	所有客户端信誉值分布	[2, 3, 5, 7, 8, ...]	可画直方图分析

        五、消融与对比实验元信息（实验级日志）
            字段名	含义	示例	说明
            use_committee_mechanism	是否启用共识机制	True / False	消融实验用
            use_reputation_mechanism	是否启用激励机制	True / False	
            client_model_type	客户端使用的模型类型	"TinyBERT" / "TinyLLaMA-QLoRA"	模型对比实验关键字段
            server_model_type	服务端使用的模型	"MiniLM"	
            aggregation_strategy	聚合策略	"FedAvg" / "FedNova"	

        示例：logs/round_001.json
        {
            "round": 1,
            "timestamp": "2025-05-26 20:35:12",
            "global_model_version": "v1",
            "num_train_clients": 5,
            "num_committee_clients": 3,
            "train_clients_ids": [1, 3, 5, 7, 9],
            "committee_clients_ids": [2, 4, 6],
            "upload_size_kb": 1278.4,      
            "download_size_kb": 1290.2,     
            "local_train_time_sec": 14.2, 
            "train_client_updates_validated": {
                "1": true,
                "3": true,
                "5": false,
                "7": true,
                "9": false
            },
            "train_client_scores": {
                "1": {"f1": 0.88, "accuracy": 0.89, "loss": 0.25},
                "3": {"f1": 0.91, "accuracy": 0.90, "loss": 0.22},
                "5": {"f1": 0.72, "accuracy": 0.75, "loss": 0.43},
                "7": {"f1": 0.90, "accuracy": 0.89, "loss": 0.23},
                "9": {"f1": 0.70, "accuracy": 0.72, "loss": 0.45}
            },
            "reputation_changes": {
                "1": 1,
                "3": 1,
                "5": -1,
                "7": 1,
                "9": -1,
                "2": 0.5,
                "4": 0.5,
                "6": 0.5
            },
            "committee_agreement_rate": 1.0,
            "aggregation_strategy": "FedAvg",
            "global_model_metrics": {
                "test_accuracy": 0.911,
                "test_precision": 0.905,
                "test_recall": 0.920,
                "test_f1_score": 0.912,
                "global_f1_delta": -0.0072
                "test_auc_roc": 0.965,
                "test_loss": 0.185,
                "confusion_matrix": {
                "TP": 92,
                "FP": 8,
                "FN": 6,
                "TN": 110
                }
            },
            "model_config": {
                "client_model_type": "TinyLLaMA-QLoRA",
                "server_model_type": "MiniLM",
                "use_committee_mechanism": true,
                "use_reputation_mechanism": true，
                "agg_method": fedavg
            }
        }

代码设计核心思想：“配置驱动 + 模块解耦”
    使用配置文件管理不同实验
项目结构
        ├── config/
        │   └── config.yaml                  # 统一配置文件
        ├── core/
        │   ├── server.py                    # 中央服务器逻辑
        │   ├── client.py                    # 客户端逻辑（训练/验证）
        │   ├── committee.py                 # 委员会验证逻辑
        │   ├── aggregator.py                # 聚合策略（信誉加权）
        │   └── selector.py                  # 客户端选择（激励/随机）
        ├── models/                      # 🧠 模型结构定义
        │   ├── base_model.py               # 所有模型的抽象基类
        │   ├── minilm_model.py             # MiniLM 架构封装
        │   └── tinybert_lora.py           # TinyBERT + LoRA 架构封装
        ├── data/
        │   └── dataset_loader.py            # 数据加载与预处理
        ├── train_rounds/
        │   └── round_runner.py              # 单轮训练逻辑（训练-验证-聚合）
        ├── experiments/
        │   └── run_exp.py                   # 主程序（支持加载不同配置）
        ├── logs/                           
        │   └── round_logs.json              # 📈 日志输出
        ├── utils/
        │   ├── logger.py                    # 实验日志
        │   ├── metrics.py                   # Precision / Recall / F1 等
        │   └── visualizer.py                # 画图工具
        ├── outputs/                     # 🧾 可视化/报告输出
        │   ├── plots/                   # 各轮图像输出（acc曲线，F1，ROC等）
        │   └── reports/                 # 自动生成的实验报告（PDF/Markdown）
        ├── requirements.txt             # Python依赖
        └── README.md



    模块化机制开关建议
        在核心逻辑中保持如下结构：
            # server.py
            if config.use_committee:
                verified_models = committee.validate(submitted_models)
            else:
                verified_models = submitted_models  # 全部接受

            # selector.py
            if config.role_selection == "credit_sort":
                selected = select_by_credit()
            else:
                selected = select_random()
        类似这样在运行阶段自动调用不同机制，而非写死在逻辑里。
    
        日志与结果可追踪	后期画图分析时不重复实验

    

数据集示例：
[
    {
        "input": "梁豪: 系统已经收到您的信息，请您等待几分钟，我们会尽快处理，确保您的账户资金安全。\n王伟: 好的，谢谢你们的帮助。",
        "output": "{\"is_fraud\": false, \"fraud_speaker\": \"\", \"reason\": \"\"}",
        "instruction": "你是一个分析诈骗案例的专家，你的任务是分析下面对话内容是否存在经济诈骗(is_fraud:<bool>)，如果存在经济诈骗，请找出正在进行诈骗行为的发言者姓名(fraud_speaker:<str>)，并给出你的分析理由(reason:<str>)，最后以json格式输出。"
    },
    {
        "input": "张俊: 放心吧，我亲自试过，不仅赚钱了，还提现了。你可以先小试一下，赚到钱了再考虑多投。\n陈娟: 哦，好吧，那我试试小额充值看看。\n张俊: 嗯嗯，我给你指引，你先充值1000元看看，放心一定会有回报的。",
        "output": "{\"is_fraud\": true, \"fraud_speaker\": \"张俊\", \"reason\": \"张俊保证通过某种钱赚方式肯定有回报，并指导陈娟充值，这种行为极有可能是与某种投资诈骗或金字塔骗局相关。这种操作手法常见于诈骗案件中，诈骗者通过承诺高收益来诱导受害者进行充值或投资。\"}",
        "instruction": "你是一个分析诈骗案例的专家，你的任务是分析下面对话内容是否存在经济诈骗(is_fraud:<bool>)，如果存在经济诈骗，请找出正在进行诈骗行为的发言者姓名(fraud_speaker:<str>)，并给出你的分析理由(reason:<str>)，最后以json格式输出。"
    }
]



对比实验	ACC	F1	RECALL	AUC	参数量
FedAvg	0.8050±0.005	0.8388±0.006	0.9479±0.001	0.8101±0.003	56058.1016kb
FedProx	0.8195±0.002	0.8325±0.003	0.9733±0.002	0.8819±0.004	56058.1016kb
FedAdam	0.8122±0.004	0.8286±0.004	0.9583±0.001	0.8092±0.003	56058.1016kb
Fed-CIC(OUR)	0.8456±0.003	0.8625±0.002	0.9596±0.003	0.9229±0.002	314.4453kb

消融实验	ACC	F1	RECALL	AUC	参数量
无激励	0.8192±0.001	0.8383±0.001	0.9239±0.003	0.8962±0.001	314.4453kb
无委员会	0.8053±0.005	0.8330±0.004	0.9180±0.006	0.8910±0.003	314.4453kb
无LoRA（全参数）	0.8551±0.004	0.8582±0.004	0.8641±0.004	0.9287±0.002	56058.1016kb
Fed-CIC(OUR)	0.8456±0.003	0.8625±0.002	0.9596±0.003	0.9229±0.002	314.4453kb

                                -------
                               | 空闲1|
                               | ...  |
                               | 空闲i|
                                -------

                               
 ------        -----------
|中央  |      |  信誉   |         -------       ---------          ---------
                                | 训练1|      |        |         |参数1  |
|服务器|——>   | 激励机制 |       | ...  |____> |本地训练|-------->|...    |---
 ------        -----------      | 训练j|      |        |         |参数n  |   |
                                -------       ---------          ---------  |
                                                                            |
                                    -------                                 |
                                | 委员会1|                                   |
                                | ...  |  <---------------------------------
                                | 委员会k|
                                -------    

                               


                              








