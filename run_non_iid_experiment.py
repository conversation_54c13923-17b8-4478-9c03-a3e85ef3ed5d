#!/usr/bin/env python3
"""
运行Non-IID实验的脚本
"""

import yaml
import os
import sys

def update_config_for_non_iid(alpha_value):
    """
    更新配置文件以进行Non-IID实验
    """
    config_path = 'config/config.yaml'
    
    # 读取配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 更新Non-IID相关配置
    config['data']['distribution_type'] = 'non_iid'
    config['data']['non_iid_alpha'] = alpha_value
    config['global']['rounds'] = 10  # 减少轮数用于快速测试
    
    # 保存配置
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"配置已更新: α = {alpha_value}, 轮数 = 10")

def run_experiment(alpha_value):
    """
    运行单个Non-IID实验
    """
    print(f"\n{'='*60}")
    print(f"开始运行 Non-IID 实验: α = {alpha_value}")
    print(f"{'='*60}")
    
    # 更新配置
    update_config_for_non_iid(alpha_value)
    
    # 运行实验
    try:
        import subprocess
        result = subprocess.run([
            sys.executable, 'experiments/run_exp.py'
        ], capture_output=True, text=True, timeout=300)  # 5分钟超时
        
        if result.returncode == 0:
            print(f"✓ α = {alpha_value} 实验完成")
            print("输出:")
            print(result.stdout[-500:])  # 显示最后500字符
        else:
            print(f"✗ α = {alpha_value} 实验失败")
            print("错误:")
            print(result.stderr[-500:])
            
    except subprocess.TimeoutExpired:
        print(f"✗ α = {alpha_value} 实验超时")
    except Exception as e:
        print(f"✗ α = {alpha_value} 实验异常: {e}")

def restore_original_config():
    """
    恢复原始配置
    """
    config_path = 'config/config.yaml'
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 恢复原始设置
    config['data']['distribution_type'] = 'non_iid'  # 保持non_iid
    config['data']['non_iid_alpha'] = 0.5  # 默认值
    config['global']['rounds'] = 50  # 恢复原始轮数
    
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print("配置已恢复到原始状态")

def main():
    """
    主函数：运行不同α值的Non-IID实验
    """
    print("Non-IID联邦学习实验")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        'config/config.yaml',
        'experiments/run_exp.py',
        'utils/non_iid_splitter.py'
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"错误: 缺少必要文件 {file_path}")
            return
    
    # 要测试的α值
    alpha_values = [1.0, 0.5, 0.1]  # 从轻度到重度异构
    
    print(f"将测试以下α值: {alpha_values}")
    print("每个实验运行10轮用于快速验证")
    
    # 运行实验
    results = {}
    for alpha in alpha_values:
        try:
            run_experiment(alpha)
            results[alpha] = "完成"
        except KeyboardInterrupt:
            print(f"\n用户中断了 α = {alpha} 的实验")
            results[alpha] = "中断"
            break
        except Exception as e:
            print(f"α = {alpha} 实验出错: {e}")
            results[alpha] = "错误"
    
    # 显示结果摘要
    print(f"\n{'='*60}")
    print("实验结果摘要:")
    print(f"{'='*60}")
    for alpha, status in results.items():
        print(f"α = {alpha:4.1f}: {status}")
    
    # 恢复配置
    restore_original_config()
    
    print(f"\n所有实验完成！")
    print("日志文件保存在 logs/ 目录中")

if __name__ == "__main__":
    main()
