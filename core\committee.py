import numpy as np
import random
from collections import Counter
from tqdm import tqdm

class Committee:
    def __init__(self, committee_clients, config):
        self.committee_clients = committee_clients  # List[Client]
        self.config = config

    def get_phase_params(self, round_idx):
        # 优先从config读取阶段参数表
        phase_table = self.config['federated'].get('phase_params', None)
        if phase_table is not None:
            for phase in phase_table:
                if phase['start'] <= round_idx <= phase['end']:
                    return phase['alpha'], phase['beta'], phase['theta'], phase['gamma']
        # 回退到内置表
        default_table = [
            (1, 10, 0.7, 0.3, 0.4, 0.35),   # 探索期
            (11, 20, 0.5, 0.5, 0.5, 0.3),   # 过渡期
            (21, 30, 0.3, 0.7, 0.6, 0.25),  # 稳定期
            (31, 1e9, 0.1, 0.8, 0.65, 0.2)  # 收敛期
        ]
        for start, end, alpha, beta, theta, gamma in default_table:
            if start <= round_idx <= end:
                return alpha, beta, theta, gamma
        return 0.5, 0.5, 0.5, 0.3  # 默认
    
    def get_committee_f1_threshold(self, round_idx):
        """
        根据当前轮次获取委员会强制拒绝的F1下降阈值
        """
        # 优先从配置的阶段性阈值中获取
        phase_thresholds = self.config['federated'].get('phase_f1_thresholds', None)
        if phase_thresholds is not None:
            for phase in phase_thresholds:
                if phase['start'] <= round_idx <= phase['end']:
                    return phase['committee']
        
        # 如果没有阶段性配置，使用默认委员会阈值
        return self.config['federated'].get('committee_force_reject_threshold', -0.02)

    def validate_f1_baseline(self, committee_client, dataloader):
        """使用委员会成员评估F1基准值"""
        committee_client.model.eval()
        # 不再需要固定阈值，evaluate会自动寻找最优阈值
        metrics = committee_client.model.evaluate(dataloader, committee_client.device)
        return metrics['f1']

    def evaluate_committee_f1_baseline(self, committee_clients, val_loaders, client_ids=None):
        # 不再需要固定阈值
        if client_ids is None:
            client_ids = [c.client_id for c in committee_clients]
        
        # ... 其他代码保持不变
        with tqdm(total=len(client_ids), desc="评估委员会F1基准", leave=True) as pbar:
            for idx, client_id in enumerate(client_ids):
                client = next(c for c in committee_clients if c.client_id == client_id)
                dataloader = val_loaders[client_id]
                metrics = client.model.evaluate(dataloader, client.device) # 移除threshold
                # ... 其他代码保持不变

    def validate_and_vote(self, lora_update, val_loaders, global_state_dict, committee_f1_baseline={}, round_idx=1):
        """委员会验证客户端更新"""
        # 不再需要固定阈值
        
        # 获取委员会验证参数
        k = min(self.config['federated'].get('committee_validate_k', 3), len(self.committee_clients))
        selected_committee = random.sample(self.committee_clients, k)
        votes = []
        metrics_list = []
        dist_list = []
        f1_delta_list = []
        score_list = []
        selected_ids = [c.client_id for c in selected_committee]
        alpha, beta, theta, gamma = self.get_phase_params(round_idx)
        
        # 获取当前轮次的委员会强制拒绝阈值
        committee_f1_threshold = self.get_committee_f1_threshold(round_idx)
        print(f"\n当前轮次{round_idx}的委员会F1强制拒绝阈值: {committee_f1_threshold}")
        
        # 直接使用本轮预先传入的committee_f1_baseline
        with tqdm(total=k, desc="委员会验证进度", leave=True) as bar:
            for client in selected_committee:
                val_loader = val_loaders[client.client_id]
                
                # 调用新的evaluate方法，无需传入阈值
                old_lora = client.model.get_lora_parameters()
                client.model.set_lora_parameters(lora_update)
                metrics = client.model.evaluate(val_loader, client.device) # 移除threshold
                new_f1 = metrics['f1']
                client.model.set_lora_parameters(old_lora)
                baseline_metrics = client.model.evaluate(val_loader, client.device) # 移除threshold
                baseline_f1 = baseline_metrics['f1']
                
                # 计算F1变化和距离
                f1_delta = new_f1 - baseline_f1
                from utils.metrics import cosine_distance
                dist = cosine_distance(lora_update, global_state_dict)
                
                # 评分和投票
                score = alpha * f1_delta + beta * (1 - dist)
                pass_score = (score >= theta) or (1 - dist >= gamma)
                if f1_delta < committee_f1_threshold:
                    pass_score = False
                    # 只在进度条下方输出一次警告
                    bar.write(f"警告: 委员{client.client_id}强制拒绝更新，F1下降{f1_delta:.4f}超过阈值{committee_f1_threshold}")
                
                votes.append(pass_score)
                metrics_list.append(metrics)
                dist_list.append(dist)
                f1_delta_list.append(f1_delta)
                score_list.append(score)
                
                # 输出进度信息
                preds = metrics.get('preds', None)
                labels = metrics.get('labels', None)
                pred_pos = sum(preds) if preds is not None else 0
                total = len(preds) if preds is not None else 0
                true_pos = sum(labels) if labels is not None else 0
                postfix = {
                    '委员': client.client_id,
                    'F1Δ': f"{f1_delta:.4f}",
                    '预测正类': f"{pred_pos}/{total}({pred_pos/total*100:.1f}%)" if total > 0 else 'N/A',
                    '真实正类': f"{true_pos}/{total}({true_pos/total*100:.1f}%)" if total > 0 else 'N/A'
                }
                bar.set_postfix(postfix)
                bar.update(1)
        
        consensus_ratio = self.config['federated'].get('committee_consensus_ratio', 2/3)
        num_accept = sum(votes)
        agreement_rate = num_accept / len(votes) if votes else 0
        accepted = agreement_rate >= consensus_ratio
        
        return {
            'accepted': accepted,
            'votes': votes,
            'metrics_list': metrics_list,
            'dist_list': dist_list,
            'f1_delta_list': f1_delta_list,
            'score_list': score_list,
            'agreement_rate': agreement_rate,
            'selected_committee_ids': selected_ids,
            'phase_params': {'alpha': alpha, 'beta': beta, 'theta': theta, 'gamma': gamma}
        }

    def update_committee_reputation(self, votes, accepted, selected_committee):
        """
        只对本次参与验证的k个委员更新信誉值。
        """
        for idx, client in enumerate(selected_committee):
            if votes[idx] == accepted:
                client.update_reputation(0.5)
            else:
                client.update_reputation(-0.5)

def evaluate_committee_f1_baseline(committee, val_loaders, global_model, config=None):
    """评估委员会成员的F1基准分数"""
    # 不再需要固定阈值
    
    f1_scores = []
    result_summary = []  # 收集所有委员的结果
    
    with tqdm(total=len(committee), desc="委员会F1基准评估") as bar:
        for idx, (member, val_loader) in enumerate(zip(committee, val_loaders)):
            # 使用新的evaluate方法
            metrics = member.model.evaluate(val_loader, member.device)
            best_threshold = metrics.get('best_threshold', 0.5)
            preds = metrics.get('preds', None)
            labels = metrics.get('labels', None)
            pred_pos = sum(preds) if preds is not None else 0
            total = len(preds) if preds is not None else 0
            true_pos = sum(labels) if labels is not None else 0
            f1 = metrics['f1']
            f1_scores.append(f1)
            
            # 收集当前委员的结果到汇总列表
            result_summary.append({
                '委员': idx,
                'F1基准分数': f1,
                '预测正类数量': pred_pos,
                '总样本数': total,
                '预测正类比例': f"{pred_pos/total*100:.1f}%" if total > 0 else 'N/A',
                '真实正类数量': true_pos,
                '真实正类比例': f"{true_pos/total*100:.1f}%" if total > 0 else 'N/A',
                '最优阈值': best_threshold
            })
            
            # 进度条中仅显示当前委员的简要信息
            postfix = {'委员': idx, 'F1': f"{f1:.4f}", '最优阈值': best_threshold}
            bar.set_postfix(postfix)
            bar.update(1)
    
    # 进度条结束后，集中输出所有结果
    print("\n--- 委员会F1基准评估结果汇总 ---")
    for result in result_summary:
        print(f"委员 {result['委员']} F1基准分数: {result['F1基准分数']:.4f} (最优阈值={result['最优阈值']:.2f})")
        print(f"  预测分布: 正类={result['预测正类数量']}/{result['总样本数']}({result['预测正类比例']}), "
              f"真实分布: 正类={result['真实正类数量']}/{result['总样本数']}({result['真实正类比例']})")
    
    # 显示平均F1分数
    avg_f1 = sum(f1_scores) / len(f1_scores) if f1_scores else 0
    print(f"\n委员会平均F1基准分数: {avg_f1:.4f}")
    
    return f1_scores 