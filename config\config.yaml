global:
  seed: 42                # 随机种子，保证实验可复现
  rounds: 50              # 联邦训练总轮数
  use_committee: true     # 是否启用委员会共识机制
  use_reputation: true    # 是否启用激励机制（信誉分）
  role_selection: credit_sort # 客户端角色分配策略：credit_sort（按信誉排序）或 random（随机）
  aggregation_strategy: credit # 聚合策略：fedavg（平均）或 credit（信誉加权）

model:
  client_model_type: huawei-noah/TinyBERT_General_4L_312D   # 客户端使用的模型（如 TinyBERT）
  server_model_type: huawei-noah/TinyBERT_General_4L_312D       # 服务器端使用的模型（如 TinyBERT，必须与客户端一致）
  lora_r: 8              # LoRA秩，越大表达能力越强，参数量越大
  lora_alpha: 32         # LoRA缩放系数，影响训练稳定性
  lora_dropout: 0.2      # LoRA dropout，防止过拟合
  # 训练相关参数
  learning_rate: 1e-4    # 基础学习率
  lora_lr_factor: 1.0    # LoRA参数学习率因子（相对于基础学习率）
  classifier_lr_factor: 2.5  # 分类头学习率因子（相对于基础学习率）
  weight_decay: 5e-4     # 权重衰减
  clip_grad_norm: 0.5    # 梯度裁剪阈值
  use_lr_scheduler: false # 是否使用学习率调度器
  local_epochs: 1        # 每个客户端本地训练轮数

loss:
  type: CrossEntropyWithPenalty # 使用我们自定义的损失函数
  pos_weight: 1.0               # 数据集平衡，此项必须为1.0
  lambda_penalty: 0.1           # 精调阶段：小幅引入logit惩罚
  gamma_margin: 0.5             # 精调阶段：使用一个更温和的边际

federated:
  num_clients: 30        # 客户端总数
  num_committee: 6       # 每轮委员会客户端数
  num_train:  14         # 每轮训练客户端数
  min_agg_models: 7      # 聚合所需最少模型数
  # f1_rollback_threshold: -0.03   # 全局回滚的F1下降阈值，超过则回滚
  # committee_force_reject_threshold: -0.02  # 委员会强制拒绝的F1下降阈值（默认更严格）
  committee_validate_k: 4        # 每个训练客户端提交后，随机选取k个委员验证
  committee_consensus_ratio: 0.5 # 共识机制通过比率（如2/3=0.67）
  # 不同阶段的F1阈值设置
  phase_f1_thresholds:
    - {start: 1, end: 10, committee: -0.02, global: -0.03}   # 探索期
    - {start: 11, end: 20, committee: -0.015, global: -0.02}  # 过渡期
    - {start: 21, end: 30, committee: -0.01, global: -0.015}  # 稳定期
    - {start: 31, end: 1000, committee: -0.005, global: -0.015} # 收敛期
  phase_params:
    - {start: 1, end: 10, alpha: 0.7, beta: 0.3, theta: 0.55, gamma: 0.65}   # 探索期
    - {start: 11, end: 20, alpha: 0.5, beta: 0.5, theta: 0.65, gamma: 0.8}   # 过渡期
    - {start: 21, end: 30, alpha: 0.3, beta: 0.7, theta: 0.7, gamma: 0.9}  # 稳定期
    - {start: 31, end: 1000, alpha: 0.2, beta: 0.8, theta: 0.75, gamma: 0.95} # 收敛期

data:
  train_path: data/train.json    # 训练集路径
  val_path: data/eval.json        # 验证集路径
  test_path: data/test.json      # 测试集路径
  batch_size: 16                 # 批量大小
  max_seq_length: 512            # 最大序列长度
  # Non-IID数据分配配置
  distribution_type: "non_iid"   # 数据分布类型: "iid" 或 "non_iid"
  non_iid_alpha: 0.5             # Dirichlet分布参数，控制异构程度，越小越异构
  min_samples_per_client: 10     # 每个客户端最少样本数

log:
  log_dir: logs/                 # 日志输出目录
  log_level: INFO                # 日志等级 