import torch
import numpy as np
from sklearn.metrics import f1_score, accuracy_score, precision_score, recall_score

def compute_metrics(y_true, y_pred):
    return {
        'accuracy': accuracy_score(y_true, y_pred),
        'f1': f1_score(y_true, y_pred),
        'precision': precision_score(y_true, y_pred),
        'recall': recall_score(y_true, y_pred)
    }

def compute_f1(y_true, y_pred):
    return f1_score(y_true, y_pred)

def cosine_distance(lora1, lora2):
    """
    计算两个LoRA参数字典的平均余弦距离
    """
    dists = []
    for k in lora1.keys():
        v1 = lora1[k].flatten().cpu().numpy()
        v2 = lora2[k].flatten().cpu().numpy()
        if np.linalg.norm(v1) == 0 or np.linalg.norm(v2) == 0:
            continue
        cos_sim = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
        dists.append(1 - cos_sim)
    return float(np.mean(dists)) if dists else 1.0 