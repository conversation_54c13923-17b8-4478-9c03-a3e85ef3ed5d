import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import ConfusionMatrixDisplay

def plot_metrics(history, save_path=None):
    """
    绘制训练过程中的acc、f1、precision、recall、loss曲线。
    history: dict，key为指标名，value为每轮的值
    """
    # 分为两个子图，一个用于loss，一个用于其他指标
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 12))
    fig.suptitle('Server-Side Training Metrics Over Rounds', fontsize=16)

    # 第一个子图绘制 F1, Accuracy, Precision, Recall
    for metric in ['f1', 'accuracy', 'precision', 'recall']:
        if metric in history:
            ax1.plot(history[metric], label=metric.capitalize(), marker='o', linestyle='--')
    ax1.set_xlabel('Round')
    ax1.set_ylabel('Score')
    ax1.set_title('Evaluation Metrics')
    ax1.legend()
    ax1.grid(True)

    # 第二个子图绘制 Loss
    if 'loss' in history:
        ax2.plot(history['loss'], label='Loss', color='red', marker='x')
    ax2.set_xlabel('Round')
    ax2.set_ylabel('Loss')
    ax2.set_title('Evaluation Loss')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.96])

    if save_path:
        plt.savefig(save_path)
    plt.close()

def plot_confusion_matrix(y_true, y_pred, labels, save_path=None):
    disp = ConfusionMatrixDisplay.from_predictions(y_true, y_pred, display_labels=labels, cmap=plt.cm.Blues)
    plt.title('Confusion Matrix')
    if save_path:
        plt.savefig(save_path)
    plt.close() 