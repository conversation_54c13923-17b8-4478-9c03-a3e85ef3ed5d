import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle, FancyArrowPatch, Circle, Ellipse
from matplotlib.path import Path
import matplotlib.patches as patches
import matplotlib as mpl

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建图形和坐标轴
fig, ax = plt.subplots(figsize=(14, 10))

# 设置背景色和边框
ax.set_facecolor('#f9f9f9')
for spine in ax.spines.values():
    spine.set_visible(True)
    spine.set_color('#dddddd')
    spine.set_linewidth(2)

# 定义颜色
COLOR_INPUT = '#e6f7ff'
COLOR_TINYBERT = '#fff2e8'
COLOR_LORA = '#f6ffed'
COLOR_CLASSIFIER = '#f0f5ff'
COLOR_LOSS = '#fff7e6'
COLOR_ARROW = '#1890ff'
COLOR_BORDER = '#555555'
COLOR_TEXT = '#333333'

# 绘制输入层
input_rect = Rectangle((1, 9), 3, 1, facecolor=COLOR_INPUT, edgecolor=COLOR_BORDER, linewidth=1.5, alpha=0.8)
ax.add_patch(input_rect)
ax.text(2.5, 9.5, '输入文本', ha='center', va='center', fontsize=12, color=COLOR_TEXT, weight='bold')
ax.text(2.5, 9.2, 'input_ids, attention_mask', ha='center', va='center', fontsize=10, color=COLOR_TEXT)

# 绘制TinyBERT大框架
tinybert_frame = Rectangle((0.5, 3), 7, 5, facecolor='#f0f0f0', edgecolor=COLOR_BORDER, linewidth=1.5, alpha=0.3)
ax.add_patch(tinybert_frame)
ax.text(4, 7.7, 'TinyBERT + LoRA', ha='center', va='center', fontsize=14, color=COLOR_TEXT, weight='bold')

# 绘制TinyBERT编码器层
encoder_rect = Rectangle((1, 4), 3, 3, facecolor=COLOR_TINYBERT, edgecolor=COLOR_BORDER, linewidth=1.5, alpha=0.8)
ax.add_patch(encoder_rect)
ax.text(2.5, 6.5, 'TinyBERT编码器', ha='center', va='center', fontsize=12, color=COLOR_TEXT, weight='bold')
ax.text(2.5, 6.1, '(固定参数)', ha='center', va='center', fontsize=10, color=COLOR_TEXT)
ax.text(2.5, 5.7, '多头自注意力', ha='center', va='center', fontsize=10, color=COLOR_TEXT)
ax.text(2.5, 5.3, '前馈网络', ha='center', va='center', fontsize=10, color=COLOR_TEXT)
ax.text(2.5, 4.9, '层归一化', ha='center', va='center', fontsize=10, color=COLOR_TEXT)
ax.text(2.5, 4.5, '...', ha='center', va='center', fontsize=10, color=COLOR_TEXT)

# 绘制LoRA适配器层
lora_rect = Rectangle((5, 4), 2, 3, facecolor=COLOR_LORA, edgecolor='green', linewidth=1.5, alpha=0.8)
ax.add_patch(lora_rect)
ax.text(6, 6.5, 'LoRA适配器', ha='center', va='center', fontsize=12, color=COLOR_TEXT, weight='bold')
ax.text(6, 6.1, '(可训练参数)', ha='center', va='center', fontsize=10, color=COLOR_TEXT)
ax.text(6, 5.7, 'lora_A (r×d)', ha='center', va='center', fontsize=10, color=COLOR_TEXT)
ax.text(6, 5.3, 'lora_B (d\'×r)', ha='center', va='center', fontsize=10, color=COLOR_TEXT)
ax.text(6, 4.9, 'α缩放因子', ha='center', va='center', fontsize=10, color=COLOR_TEXT)
ax.text(6, 4.5, '...', ha='center', va='center', fontsize=10, color=COLOR_TEXT)

# 绘制并行连接和加法节点
parallel_arrow1 = FancyArrowPatch((2.5, 8.5), (2.5, 7), arrowstyle='->', linewidth=2, color=COLOR_ARROW)
ax.add_patch(parallel_arrow1)

# 添加池化层
pooling_rect = Rectangle((2, 2.2), 3, 0.6, facecolor='white', edgecolor=COLOR_BORDER, linewidth=1.5, alpha=0.8)
ax.add_patch(pooling_rect)
ax.text(3.5, 2.5, '池化层 [CLS]', ha='center', va='center', fontsize=11, color=COLOR_TEXT)

# 绘制Dropout层
dropout_rect = Rectangle((2, 1.4), 3, 0.6, facecolor='white', edgecolor=COLOR_BORDER, linewidth=1.5, alpha=0.8)
ax.add_patch(dropout_rect)
ax.text(3.5, 1.7, 'Dropout', ha='center', va='center', fontsize=11, color=COLOR_TEXT)

# 绘制分类头层
classifier_rect = Rectangle((2, 0.6), 3, 0.6, facecolor=COLOR_CLASSIFIER, edgecolor='green', linewidth=1.5, alpha=0.8)
ax.add_patch(classifier_rect)
ax.text(3.5, 0.9, '分类头 (Linear, 可训练)', ha='center', va='center', fontsize=11, color=COLOR_TEXT)

# 绘制输出层
output_rect = Rectangle((8, 0.6), 2, 0.6, facecolor='#ffe7e7', edgecolor=COLOR_BORDER, linewidth=1.5, alpha=0.8)
ax.add_patch(output_rect)
ax.text(9, 0.9, '输出', ha='center', va='center', fontsize=12, color=COLOR_TEXT)

# 绘制损失函数层
loss_rect = Rectangle((8, 4), 3, 2, facecolor=COLOR_LOSS, edgecolor=COLOR_BORDER, linewidth=1.5, alpha=0.8)
ax.add_patch(loss_rect)
ax.text(9.5, 5.5, 'CrossEntropyWithPenalty', ha='center', va='center', fontsize=12, color=COLOR_TEXT, weight='bold')
ax.text(9.5, 5.1, '加权BCE损失', ha='center', va='center', fontsize=10, color=COLOR_TEXT)
ax.text(9.5, 4.8, '正类logit惩罚', ha='center', va='center', fontsize=10, color=COLOR_TEXT)
ax.text(9.5, 4.5, '处理样本不平衡', ha='center', va='center', fontsize=10, color=COLOR_TEXT)

# 绘制标签输入
label_rect = Rectangle((8, 6.5), 3, 0.8, facecolor=COLOR_INPUT, edgecolor=COLOR_BORDER, linewidth=1.5, alpha=0.8)
ax.add_patch(label_rect)
ax.text(9.5, 6.9, '真实标签', ha='center', va='center', fontsize=12, color=COLOR_TEXT)

# 绘制连接箭头
arrow_style = dict(arrowstyle='->', linewidth=2, color=COLOR_ARROW)

# 编码器到池化层
encoder_to_pooling = FancyArrowPatch((2.5, 4), (3.5, 2.8), **arrow_style)
ax.add_patch(encoder_to_pooling)

# 池化层到Dropout
pooling_to_dropout = FancyArrowPatch((3.5, 2.2), (3.5, 2), **arrow_style)
ax.add_patch(pooling_to_dropout)

# Dropout到分类头
dropout_to_classifier = FancyArrowPatch((3.5, 1.4), (3.5, 1.2), **arrow_style)
ax.add_patch(dropout_to_classifier)

# 分类头到输出
classifier_to_output = FancyArrowPatch((5, 0.9), (8, 0.9), **arrow_style)
ax.add_patch(classifier_to_output)

# 输出到损失函数
output_to_loss = FancyArrowPatch((9, 1.2), (9.5, 4), **arrow_style)
ax.add_patch(output_to_loss)

# 标签到损失函数
label_to_loss = FancyArrowPatch((9.5, 6.5), (9.5, 6), **arrow_style)
ax.add_patch(label_to_loss)

# 添加LoRA并行连接和加法节点
add_circle = Circle((4, 5.5), 0.3, facecolor='white', edgecolor='green', linewidth=1.5)
ax.add_patch(add_circle)
ax.text(4, 5.5, '+', ha='center', va='center', fontsize=14, color='green', weight='bold')

lora_to_add = FancyArrowPatch((5, 5.5), (4.3, 5.5), arrowstyle='->', linewidth=2, color='green')
ax.add_patch(lora_to_add)

encoder_to_add = FancyArrowPatch((4, 5.5), (4, 4), arrowstyle='->', linewidth=2, color=COLOR_ARROW)
ax.add_patch(encoder_to_add)

# 添加反向传播箭头（虚线）
backprop_style = dict(arrowstyle='<-', linestyle='dashed', linewidth=1.5, color='red')

# 损失到LoRA的反向传播
backprop_loss_to_lora = FancyArrowPatch((8, 5), (7, 5.5), **backprop_style)
ax.add_patch(backprop_loss_to_lora)
ax.text(7.5, 5.2, '反向传播', ha='center', va='center', fontsize=10, color='red')

# 损失到分类头的反向传播
backprop_loss_to_classifier = FancyArrowPatch((8.5, 3), (5, 0.9), **backprop_style)
ax.add_patch(backprop_loss_to_classifier)

# 添加LoRA公式说明
formula_rect = Rectangle((0.5, 0.2), 7, 0.3, facecolor='none', edgecolor='none')
ax.add_patch(formula_rect)
ax.text(4, 0.2, 'LoRA公式: Y = W_frozen·X + α·(LoRA_B·LoRA_A)·X / r', ha='center', va='center', fontsize=11, color='green')

# 添加标题
ax.text(5, 10.5, '训练客户端本地训练神经网络架构 (LoRA并行应用)', ha='center', va='center', fontsize=16, color=COLOR_TEXT, weight='bold')

# 添加注释说明
ax.text(9, 2.5, '注：\n1. LoRA适配器与TinyBERT线性层并行应用\n2. 只有LoRA适配器和分类头参数参与训练\n3. TinyBERT参数固定不更新\n4. 每个注意力层都有自己的LoRA参数\n5. 训练完成后只上传LoRA参数和分类头参数', 
        fontsize=10, color=COLOR_TEXT, ha='left', va='center')

# 设置坐标轴
ax.set_xlim(0, 12)
ax.set_ylim(0, 11)
ax.axis('off')

# 保存图像
plt.tight_layout()
plt.savefig('local_training_architecture_improved.png', dpi=300, bbox_inches='tight')
plt.close()

print("改进后的训练客户端本地训练神经网络架构图已保存为 'local_training_architecture_improved.png'") 