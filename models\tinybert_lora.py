import torch
import torch.nn as nn
from transformers import AutoModel, AutoConfig
from .base_model import BaseModel
import math
import numpy as np

class TinyBERTLoRA(BaseModel):
    def __init__(self, model_name='huawei-noah/TinyBERT_General_4L_312D', lora_r=8, lora_alpha=16, lora_dropout=0.1, num_labels=1):
        super().__init__()
        self.config = AutoConfig.from_pretrained(model_name, num_labels=num_labels)
        self.bert = AutoModel.from_pretrained(model_name, config=self.config)
        self.dropout = nn.Dropout(lora_dropout)
        self.classifier = nn.Linear(self.config.hidden_size, num_labels)
        # LoRA参数
        self.lora_r = lora_r
        self.lora_alpha = lora_alpha
        self.lora_dropout = lora_dropout
        self._inject_lora()

    def _inject_lora(self):
        # 只在BERT的attention中注入LoRA
        for name, module in self.bert.named_modules():
            if 'attention' in name and isinstance(module, nn.Linear):
                module.lora_A = nn.Parameter(torch.zeros((self.lora_r, module.in_features)))
                module.lora_B = nn.Parameter(torch.zeros((module.out_features, self.lora_r)))
                nn.init.kaiming_uniform_(module.lora_A, a=math.sqrt(5))
                nn.init.zeros_(module.lora_B)
                module.lora_alpha = self.lora_alpha
                module.lora_dropout = nn.Dropout(self.lora_dropout)
                # 覆盖forward
                module._old_forward = module.forward
                def lora_forward(x, module=module):
                    result = module._old_forward(x)
                    lora_out = module.lora_dropout(x) @ module.lora_A.T @ module.lora_B.T * (module.lora_alpha / self.lora_r)
                    return result + lora_out
                module.forward = lora_forward

    def forward(self, input_ids, attention_mask=None, **kwargs):
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        pooled = outputs.last_hidden_state[:, 0]  # 取[CLS]
        pooled = self.dropout(pooled)
        logits = self.classifier(pooled)
        return logits

    def get_lora_parameters(self):
        lora_params = {}
        # 收集LoRA参数
        for name, module in self.bert.named_modules():
            if hasattr(module, 'lora_A') and hasattr(module, 'lora_B'):
                lora_params[f'{name}.lora_A'] = module.lora_A.data.cpu().clone()
                lora_params[f'{name}.lora_B'] = module.lora_B.data.cpu().clone()
        
        # 收集分类头参数 - 这是关键修改，确保分类头参数也被包含进来
        lora_params['classifier.weight'] = self.classifier.weight.data.cpu().clone()
        lora_params['classifier.bias'] = self.classifier.bias.data.cpu().clone()
        
        return lora_params

    def set_lora_parameters(self, lora_state_dict):
        # 设置LoRA参数
        for name, module in self.bert.named_modules():
            if hasattr(module, 'lora_A') and hasattr(module, 'lora_B'):
                if f'{name}.lora_A' in lora_state_dict:
                    module.lora_A.data.copy_(lora_state_dict[f'{name}.lora_A'])
                if f'{name}.lora_B' in lora_state_dict:
                    module.lora_B.data.copy_(lora_state_dict[f'{name}.lora_B'])
        
        # 设置分类头参数 - 这是关键修改，确保分类头参数也被更新
        if 'classifier.weight' in lora_state_dict:
            self.classifier.weight.data.copy_(lora_state_dict['classifier.weight'])
        if 'classifier.bias' in lora_state_dict:
            self.classifier.bias.data.copy_(lora_state_dict['classifier.bias'])

    def evaluate(self, dataloader, device):
        self.eval()
        true_labels = []
        all_probs = []
        total_loss = 0
        criterion = nn.BCEWithLogitsLoss()

        # 1. 收集所有真实标签和预测概率，并计算损失
        with torch.no_grad():
            for batch in dataloader:
                input_ids = batch['input_ids'].to(device)
                attention_mask = batch['attention_mask'].to(device)
                label = batch['labels'].to(device).float()
                
                logits = self.forward(input_ids, attention_mask)
                
                # 计算评估损失
                loss = criterion(logits.squeeze(-1), label.squeeze(-1))
                total_loss += loss.item()

                probs = torch.sigmoid(logits.squeeze(-1))
                
                all_probs.extend(probs.cpu().tolist())
                true_labels.extend(label.squeeze(-1).cpu().tolist())
        
        from sklearn.metrics import f1_score, accuracy_score, precision_score, recall_score, confusion_matrix, roc_auc_score

        # 2. 寻找最优F1阈值
        best_f1 = 0
        best_threshold = 0.5
        thresholds = np.linspace(0.1, 0.9, 17) # 0.1, 0.15, ..., 0.9

        for threshold in thresholds:
            preds = (np.array(all_probs) >= threshold).astype(int)
            f1 = f1_score(true_labels, preds, zero_division=0)
            if f1 > best_f1:
                best_f1 = f1
                best_threshold = threshold
        
        # 3. 使用最优阈值计算最终指标
        final_preds = (np.array(all_probs) >= best_threshold).astype(int)

        cm = confusion_matrix(true_labels, final_preds)
        tn, fp, fn, tp = 0, 0, 0, 0
        if cm.size == 4:
            tn, fp, fn, tp = cm.ravel()
            
        auc_roc = 0.0
        if len(set(true_labels)) > 1:
            try:
                auc_roc = roc_auc_score(true_labels, all_probs)
            except ValueError:
                auc_roc = 0.0
        
        avg_loss = total_loss / len(dataloader) if dataloader else 0
        return {
            'loss': avg_loss,
            'accuracy': accuracy_score(true_labels, final_preds),
            'f1': best_f1,
            'precision': precision_score(true_labels, final_preds, zero_division=0),
            'recall': recall_score(true_labels, final_preds, zero_division=0),
            'auc_roc': auc_roc,
            'best_threshold': best_threshold,
            'confusion_matrix': {
                'TP': int(tp), 'FP': int(fp),
                'FN': int(fn), 'TN': int(tn)
            }
        } 