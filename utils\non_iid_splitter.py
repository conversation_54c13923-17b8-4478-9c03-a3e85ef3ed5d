import numpy as np
import json
from collections import defaultdict

class NonIIDDataSplitter:
    """
    Non-IID数据分配器，使用Dirichlet分布创建异构数据分割
    """
    
    def __init__(self, alpha=0.5, min_samples_per_client=10, seed=42):
        """
        初始化Non-IID数据分配器
        
        Args:
            alpha (float): Dirichlet分布参数，控制异构程度，越小越异构
            min_samples_per_client (int): 每个客户端最少样本数
            seed (int): 随机种子
        """
        self.alpha = alpha
        self.min_samples_per_client = min_samples_per_client
        self.seed = seed
        np.random.seed(seed)
    
    def get_labels_from_dataset(self, dataset):
        """
        从数据集中提取标签
        
        Args:
            dataset: PyTorch Dataset对象
            
        Returns:
            list: 标签列表
        """
        labels = []
        for i in range(len(dataset)):
            sample = dataset.samples[i]
            label_json = sample['output']
            if isinstance(label_json, str):
                label_json = json.loads(label_json)
            label = int(label_json['is_fraud'])
            labels.append(label)
        return labels
    
    def create_label_skew_split(self, dataset, num_clients):
        """
        创建标签分布偏斜的Non-IID数据分割
        
        Args:
            dataset: PyTorch Dataset对象
            num_clients (int): 客户端数量
            
        Returns:
            list: 每个客户端的数据索引列表
        """
        # 获取所有标签
        labels = self.get_labels_from_dataset(dataset)
        num_samples = len(labels)
        
        # 按标签分组索引
        label_indices = defaultdict(list)
        for idx, label in enumerate(labels):
            label_indices[label].append(idx)
        
        # 获取标签类别
        num_classes = len(label_indices)
        class_labels = list(label_indices.keys())
        
        print(f"数据集统计: 总样本数={num_samples}, 类别数={num_classes}")
        for label in class_labels:
            print(f"  标签{label}: {len(label_indices[label])}个样本")
        
        # 为每个客户端分配数据
        client_indices = [[] for _ in range(num_clients)]
        
        # 对每个类别使用Dirichlet分布分配
        for label in class_labels:
            indices = label_indices[label]
            num_label_samples = len(indices)
            
            # 生成Dirichlet分布的比例
            proportions = np.random.dirichlet([self.alpha] * num_clients)
            
            # 根据比例分配样本
            proportions = proportions / proportions.sum()  # 归一化
            assigned_samples = (proportions * num_label_samples).astype(int)
            
            # 处理由于取整导致的样本数不匹配
            diff = num_label_samples - assigned_samples.sum()
            for i in range(abs(diff)):
                if diff > 0:
                    assigned_samples[i % num_clients] += 1
                else:
                    if assigned_samples[i % num_clients] > 0:
                        assigned_samples[i % num_clients] -= 1
            
            # 随机打乱索引并分配
            np.random.shuffle(indices)
            start_idx = 0
            
            for client_id in range(num_clients):
                end_idx = start_idx + assigned_samples[client_id]
                client_indices[client_id].extend(indices[start_idx:end_idx])
                start_idx = end_idx
        
        # 确保每个客户端至少有最少样本数
        self._ensure_min_samples(client_indices, num_clients)
        
        # 打乱每个客户端的数据索引
        for client_id in range(num_clients):
            np.random.shuffle(client_indices[client_id])
        
        # 打印分配统计
        self._print_split_statistics(client_indices, labels)
        
        return client_indices
    
    def _ensure_min_samples(self, client_indices, num_clients):
        """
        确保每个客户端至少有最少样本数
        """
        for client_id in range(num_clients):
            if len(client_indices[client_id]) < self.min_samples_per_client:
                # 从样本数最多的客户端转移一些样本
                max_client = max(range(num_clients), key=lambda x: len(client_indices[x]))
                needed = self.min_samples_per_client - len(client_indices[client_id])
                
                if len(client_indices[max_client]) > needed + self.min_samples_per_client:
                    # 转移样本
                    transferred = client_indices[max_client][-needed:]
                    client_indices[max_client] = client_indices[max_client][:-needed]
                    client_indices[client_id].extend(transferred)
    
    def _print_split_statistics(self, client_indices, labels):
        """
        打印数据分割统计信息
        """
        print(f"\nNon-IID数据分割统计 (α={self.alpha}):")
        print("-" * 50)
        
        total_samples = sum(len(indices) for indices in client_indices)
        label_stats = []
        
        for client_id, indices in enumerate(client_indices):
            if len(indices) == 0:
                continue
                
            # 统计该客户端的标签分布
            client_labels = [labels[idx] for idx in indices]
            label_counts = defaultdict(int)
            for label in client_labels:
                label_counts[label] += 1
            
            fraud_ratio = label_counts[1] / len(indices) if len(indices) > 0 else 0
            label_stats.append(fraud_ratio)
            
            print(f"客户端{client_id:2d}: {len(indices):3d}样本, "
                  f"欺诈比例: {fraud_ratio:.3f} "
                  f"(正常:{label_counts[0]}, 欺诈:{label_counts[1]})")
        
        # 计算异构性指标
        if label_stats:
            mean_fraud_ratio = np.mean(label_stats)
            std_fraud_ratio = np.std(label_stats)
            print(f"\n异构性统计:")
            print(f"  平均欺诈比例: {mean_fraud_ratio:.3f}")
            print(f"  欺诈比例标准差: {std_fraud_ratio:.3f}")
            print(f"  变异系数: {std_fraud_ratio/mean_fraud_ratio:.3f}")
        
        print(f"  总样本数: {total_samples}")
        print("-" * 50)
    
    def create_iid_split(self, dataset, num_clients):
        """
        创建IID数据分割（作为对比）
        
        Args:
            dataset: PyTorch Dataset对象
            num_clients (int): 客户端数量
            
        Returns:
            list: 每个客户端的数据索引列表
        """
        num_samples = len(dataset)
        indices = np.random.permutation(num_samples)
        
        # 平均分配
        client_indices = np.array_split(indices, num_clients)
        client_indices = [indices.tolist() for indices in client_indices]
        
        # 打印统计
        labels = self.get_labels_from_dataset(dataset)
        self._print_split_statistics(client_indices, labels)
        
        return client_indices

def create_data_split(dataset, num_clients, distribution_type="iid", alpha=0.5, 
                     min_samples_per_client=10, seed=42):
    """
    创建数据分割的便捷函数
    
    Args:
        dataset: PyTorch Dataset对象
        num_clients (int): 客户端数量
        distribution_type (str): 分布类型，"iid" 或 "non_iid"
        alpha (float): Non-IID的Dirichlet参数
        min_samples_per_client (int): 每个客户端最少样本数
        seed (int): 随机种子
        
    Returns:
        list: 每个客户端的数据索引列表
    """
    splitter = NonIIDDataSplitter(alpha=alpha, 
                                  min_samples_per_client=min_samples_per_client, 
                                  seed=seed)
    
    if distribution_type.lower() == "non_iid":
        return splitter.create_label_skew_split(dataset, num_clients)
    else:
        return splitter.create_iid_split(dataset, num_clients)
