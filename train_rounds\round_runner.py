import time
import torch
import numpy as np
from core.selector import select_by_credit_with_history, select_random
from core.aggregator import aggregate
from core.committee import Committee, evaluate_committee_f1_baseline
from tqdm import tqdm
from sklearn.metrics import roc_auc_score, confusion_matrix

class RoundRunner:
    def __init__(self, server, clients, config, train_loaders, val_loaders, test_loader):
        self.server = server
        self.clients = clients
        self.config = config
        self.train_loaders = train_loaders
        self.val_loaders = val_loaders
        self.test_loader = test_loader
        self.round_logs = []
        self.last_f1_score = None  # 用于计算global_f1_delta
        self.last_committee_ids = []
        self.last_train_ids = []
        self.last_idle_ids = []
        self.last_global_metrics = None

    def run_round(self, round_idx):
        # 基础日志信息
        log = {
            'round': round_idx,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'global_model_version': f"v{self.server.global_model_version}",
        }
        
        # 显示信誉值排名
        print("\n信誉值排名:")
        sorted_clients = sorted(self.clients, key=lambda c: c.credit, reverse=True)
        for i, client in enumerate(sorted_clients):  # 显示所有客户端
            print(f"  Rank {i+1}: Client {client.client_id} (信誉值: {client.credit:.2f})")
        
        # 记录上一轮角色
        if round_idx == 1:
            last_committee_ids = []
            last_train_ids = [c.client_id for c in self.clients]
            last_idle_ids = []
        else:
            last_committee_ids = self.last_committee_ids
            last_train_ids = self.last_train_ids
            last_idle_ids = self.last_idle_ids
        if self.config['global']['role_selection'] == 'credit_sort':
            committee, train_clients, idle_clients = select_by_credit_with_history(
                self.clients, last_committee_ids, last_train_ids, last_idle_ids,
                self.config['federated']['num_committee'], self.config['federated']['num_train'])
        else:
            committee, train_clients, idle_clients = select_random(
                self.clients, self.config['federated']['num_committee'], self.config['federated']['num_train'])
        # 保存本轮角色，为下一轮用
        self.last_committee_ids = [c.client_id for c in committee]
        self.last_train_ids = [c.client_id for c in train_clients]
        self.last_idle_ids = [c.client_id for c in idle_clients]
        
        # 添加客户端数量信息
        log['num_train_clients'] = len(train_clients)
        log['num_committee_clients'] = len(committee)
        
        # 打印客户端分配情况
        print("\n客户端角色分配:")
        print(f"  训练客户端: {[c.client_id for c in train_clients]}")
        print(f"  委员会客户端: {[c.client_id for c in committee]}")
        print(f"  空闲客户端: {[c.client_id for c in idle_clients]}")
        
        log['committee_clients_ids'] = [c.client_id for c in committee]
        log['train_clients_ids'] = [c.client_id for c in train_clients]
        log['idle_clients_ids'] = [c.client_id for c in idle_clients]
        
        # 每轮动态分配数据
        all_client_ids = [c.client_id for c in self.clients]
        committee_ids = [c.client_id for c in committee]
        train_and_idle_ids = [c.client_id for c in train_clients + idle_clients]
        # 训练集划分
        train_indices = np.array_split(np.random.permutation(len(self.server.train_dataset)), len(train_and_idle_ids))
        train_loaders = {}
        for idx, cid in enumerate(train_and_idle_ids):
            subset = torch.utils.data.Subset(self.server.train_dataset, train_indices[idx])
            train_loaders[cid] = torch.utils.data.DataLoader(subset, batch_size=self.config['data']['batch_size'], shuffle=True)
        # 验证集分配重构：D_shared + D_i
        val_dataset = self.server.val_dataset
        val_indices = np.arange(len(val_dataset))
        np.random.shuffle(val_indices)
        shared_size = min(500, int(0.2 * len(val_indices)))  # D_shared 500条或20%
        overlap_ratio = 0.25  # 25%重叠
        num_committee = len(committee)
        # 1. 采样D_shared
        shared_indices = val_indices[:shared_size]
        # 2. 剩余分配给各委员，允许重叠
        private_indices_pool = val_indices[shared_size:]
        private_size = max(1, int((len(private_indices_pool) / num_committee) * (1 + overlap_ratio)))
        committee_private_indices = []
        for i in range(num_committee):
            # 允许重叠采样
            indices = np.random.choice(private_indices_pool, size=private_size, replace=False if private_size <= len(private_indices_pool) else True)
            committee_private_indices.append(indices)
        # 构建DataLoader
        D_shared_loader = torch.utils.data.DataLoader(torch.utils.data.Subset(val_dataset, shared_indices), batch_size=self.config['data']['batch_size'], shuffle=False)
        committee_val_loaders = []
        for indices in committee_private_indices:
            loader = torch.utils.data.DataLoader(torch.utils.data.Subset(val_dataset, indices), batch_size=self.config['data']['batch_size'], shuffle=False)
            committee_val_loaders.append(loader)
        # 打印分配情况
        print(f"[验证集分配] 公共基准集 D_shared: {len(shared_indices)} 条")
        for i, indices in enumerate(committee_private_indices):
            print(f"[验证集分配] 委员{i} 私有验证集: {len(indices)} 条（与D_shared重叠: {len(set(indices) & set(shared_indices))} 条）")
        # 2.2 各委员用各自D_i进行后续验证
        self.val_loaders = {client.client_id: loader for client, loader in zip(committee, committee_val_loaders)}
        self.train_loaders = train_loaders

        # 1. 先下发LoRA参数
        global_lora = self.server.broadcast_lora()
        download_size_dict = {}
        for client in train_clients:
            download_size_dict[str(client.client_id)] = self._calculate_params_size(global_lora)
        log['download_size_kb'] = download_size_dict
        print(f"\n下发全局LoRA参数完成 (每个客户端下载: {list(download_size_dict.values())[0]:.2f} KB)")

        # 2. 基准F1评估：随机选一个委员在D_shared上评估，所得F1作为所有委员的基准分数
        ref_idx = np.random.randint(num_committee)
        ref_member = committee[ref_idx]
        print(f"选定委员{ref_idx}（client_id={ref_member.client_id}）在D_shared上评估F1作为基准")
        # 先同步全局参数
        old_lora = ref_member.model.get_lora_parameters()
        ref_member.model.set_lora_parameters(global_lora)
        metrics = ref_member.model.evaluate(D_shared_loader, ref_member.device)
        f1_baseline = metrics['f1']
        ref_member.model.set_lora_parameters(old_lora)
        print(f"D_shared基准F1分数: {f1_baseline:.4f} (最优阈值={metrics['best_threshold']:.2f})")
        committee_f1_baseline = {client.client_id: f1_baseline for client in committee}
        
        # 3. 训练客户端开始本地训练，然后委员会验证
        client_updates = []
        train_client_scores = {}
        reputation_changes = {}  # 记录信誉值变化
        local_train_times = {}
        upload_size_dict = {}
        train_loss_dict = {}
        train_client_updates_validated = {}
        committee_votes = []
        print("\n开始本地训练与委员会验证:")
        for client in train_clients:
            print(f"\n客户端 {client.client_id} 第{round_idx}轮本地训练:")
            client.download_lora(global_lora)
            loader = self.train_loaders[client.client_id]
            train_start_time = time.time()
            # 调用client.local_train，自动支持多轮训练
            client.local_train(loader)
            train_time = time.time() - train_start_time
            local_train_times[str(client.client_id)] = train_time
            # 评估训练后的客户端模型
            metrics = client.model.evaluate(loader, client.device)
            avg_loss = 0.0  # 如需统计loss可在local_train返回
            train_loss_dict[str(client.client_id)] = avg_loss
            train_client_scores[str(client.client_id)] = {
                'f1': metrics['f1'],
                'accuracy': metrics['accuracy'],
                'loss': avg_loss,
                'threshold': metrics['best_threshold']
            }
            lora_update = client.model.get_lora_parameters()
            upload_size = self._calculate_params_size(lora_update)
            upload_size_dict[str(client.client_id)] = upload_size
            print(f"  客户端 {client.client_id} 训练完成，上传LoRA参数 (大小: {upload_size:.2f} KB, 耗时: {train_time:.2f}秒)")
            # 获取本轮委员会评估基准
            if self.last_global_metrics is None:
                global_f1_baseline = 0.0
            else:
                global_f1_baseline = self.last_global_metrics['f1']
            # 委员会验证
            print(f"  客户端 {client.client_id} 委员会验证:")
            committee_result = Committee(committee, self.config).validate_and_vote(
                lora_update, self.val_loaders, global_lora, committee_f1_baseline=committee_f1_baseline, round_idx=round_idx)
            accepted = committee_result['accepted']
            train_client_updates_validated[str(client.client_id)] = accepted
            if 'selected_committee_ids' not in log:
                log['selected_committee_ids'] = {}
            log['selected_committee_ids'][str(client.client_id)] = committee_result['selected_committee_ids']
            if 'votes' not in log:
                log['votes'] = {}
            log['votes'][str(client.client_id)] = committee_result['votes']
            if accepted:
                client_updates.append(lora_update)
            # 输出每个委员的评估详情
            print("    委员会成员评估详情：")
            for comm_id, f1_delta, dist, score, vote in zip(
                    committee_result['selected_committee_ids'],
                    committee_result['f1_delta_list'],
                    committee_result['dist_list'],
                    committee_result['score_list'],
                    committee_result['votes']):
                print(f"      委员 {comm_id} | F1提升: {f1_delta:+.4f} | CosineDist: {dist:.4f} | 综合评分: {score:.4f} | {'同意' if vote else '反对'}")
            phase = committee_result['phase_params']
            print(f"    本阶段参数: α={phase['alpha']}, β={phase['beta']}, θ={phase['theta']}, γ={phase['gamma']}")
            print(f"    本次更新{'通过' if accepted else '未通过'}（赞成: {sum(committee_result['votes'])}/{len(committee_result['votes'])}，参与委员: {committee_result['selected_committee_ids']}）")
            committee_votes.append(committee_result['votes'])
            Committee(committee, self.config).update_committee_reputation(
                committee_result['votes'], accepted,
                [c for c in committee if c.client_id in committee_result['selected_committee_ids']]
            )
        
        # 记录委员会成员信誉变化
        for client in committee:
            reputation_changes[str(client.client_id)] = client.credit - reputation_changes.get(str(client.client_id), client.credit)
        
        # 计算委员会一致率
        committee_agreement_rate = self._calculate_agreement_rate(committee_votes)
        
        log['train_client_updates_validated'] = train_client_updates_validated
        log['accepted_updates'] = len(client_updates)
        log['reputation_changes'] = reputation_changes
        log['committee_agreement_rate'] = committee_agreement_rate
        
        print(f"\n委员会验证完成，{len(client_updates)}/{len(train_clients)} 客户端更新被接受 (一致率: {committee_agreement_rate:.2f})")
        
        # 聚合
        if len(client_updates) >= self.config['federated']['min_agg_models']:
            print(f"\n聚合 {len(client_updates)} 个客户端LoRA参数 (策略: {self.config['global']['aggregation_strategy']})...")
            agg_lora = {}
            agg_keys = list(client_updates[0].keys()) if client_updates else []
            pbar_agg = tqdm(total=len(agg_keys), desc="中央服务器聚合进度", leave=True)
            for k in agg_keys:
                agg_lora[k] = torch.stack([c[k] for c in client_updates], 0).mean(0)
                pbar_agg.update(1)
            pbar_agg.close()
            self.server.model.set_lora_parameters(agg_lora)
            print("聚合完成，更新全局模型")
        else:
            print(f"\n接受的更新数量不足 (需要{self.config['federated']['min_agg_models']}个)，保持上一轮模型")
        
        # 记录聚合策略
        log['aggregation_strategy'] = self.config['global']['aggregation_strategy']
        
        # 服务端评估
        print("\n服务端在测试集上评估模型...")
        
        # 直接调用服务端的evaluate方法进行评估，它会自动寻找最优阈值
        metrics = self.server.evaluate(self.test_loader)
        
        # 计算F1变化
        if self.last_f1_score is not None:
            delta = metrics['f1'] - self.last_f1_score
            metrics['global_f1_delta'] = delta
        else:
            metrics['global_f1_delta'] = 0.0
        
        self.last_f1_score = metrics['f1']
        
        # 添加全局模型评估结果
        log['global_model_metrics'] = metrics
        
        print(f"评估完成: Accuracy={metrics['accuracy']:.4f}, F1={metrics['f1']:.4f}, Precision={metrics['precision']:.4f}, Recall={metrics['recall']:.4f}, AUC={metrics.get('auc_roc', 0):.4f}")
        print(f"混淆矩阵: TP={metrics['confusion_matrix']['TP']}, FP={metrics['confusion_matrix']['FP']}, FN={metrics['confusion_matrix']['FN']}, TN={metrics['confusion_matrix']['TN']}")
        
        # 回滚机制
        rollback = self.server.maybe_rollback(metrics, round_idx)
        log['rollback'] = rollback
        if rollback:
            print("性能下降，已回滚到上一轮模型")
        
        # 信誉分记录
        log['reputation_distribution'] = [c.credit for c in self.clients]
        log['avg_reputation_score'] = sum(log['reputation_distribution']) / len(log['reputation_distribution'])
        print(f"平均信誉分: {log['avg_reputation_score']:.2f}")
        
        # 添加实验配置信息
        log['model_config'] = {
            'client_model_type': self.config['model']['client_model_type'],
            'server_model_type': self.config['model']['server_model_type'],
            'use_committee_mechanism': self.config['global']['use_committee'],
            'use_reputation_mechanism': self.config['global']['use_reputation'],
            'agg_method': self.config['global']['aggregation_strategy']
        }
        
        # 记录本轮所有客户端角色
        for client in self.clients:
            if client in committee:
                client.update_role_history('committee')
            elif client in train_clients:
                client.update_role_history('train')
            elif client in idle_clients:
                client.update_role_history('idle')

        # 激励机制：统计奖励/惩罚
        for client in train_clients:
            cid = str(client.client_id)
            accepted = train_client_updates_validated.get(cid, False)
            if accepted:
                client.update_credit(1)
                client.update_si(1)
            else:
                client.update_credit(-1)
                client.update_pi(1)
        # 委员会成员奖励/惩罚
        for client in committee:
            cid = str(client.client_id)
            for tcid, comm_ids in log['selected_committee_ids'].items():
                if client.client_id in comm_ids:
                    idx = comm_ids.index(client.client_id)
                    votes = log['votes'][tcid]
                    accepted = log['train_client_updates_validated'][tcid]
                    vote = votes[idx]
                    if accepted:
                        client.update_credit(0.5)
                        client.update_si(1)
                    else:
                        if vote:
                            client.update_credit(-0.5)
                            client.update_pi(1)
                        else:
                            client.update_credit(0.5)
                            client.update_si(1)
        # 三轮内三角色奖励
        for client in self.clients:
            roles = client.get_recent_roles(3)
            if set(roles) == {'committee', 'train', 'idle'}:
                client.update_credit(0.5)
                client.update_si(1)
        # 连续两轮空闲惩罚
        for client in self.clients:
            roles = client.get_recent_roles(2)
            if roles == ['idle', 'idle']:
                client.update_credit(-0.2)
                client.update_pi(1)
        # 非参与客户端信誉衰减
        for client in self.clients:
            if client not in committee and client not in train_clients and client not in idle_clients:
                client.credit *= 0.98
        # 最终log归一化
        for client in self.clients:
            client.apply_log_credit_formula(alpha=1.0, beta=1.2)
        
        # 保存日志
        self.server.save_round_log(log)
        self.round_logs.append(log)
        self.last_global_metrics = metrics  # 记录本轮评估结果
        return log
    
    def _calculate_params_size(self, params_dict):
        """计算参数字典的大小（KB）"""
        size_bytes = 0
        for k, v in params_dict.items():
            size_bytes += v.element_size() * v.nelement()
        return size_bytes / 1024  # 转换为KB
    
    def _calculate_agreement_rate(self, votes_list):
        """计算委员会投票一致率"""
        if not votes_list:
            return 0.0
        
        agreements = 0
        total_pairs = 0
        
        for votes in votes_list:
            n = len(votes)
            if n <= 1:
                continue
                
            for i in range(n):
                for j in range(i+1, n):
                    total_pairs += 1
                    if votes[i] == votes[j]:
                        agreements += 1
        
        return agreements / total_pairs if total_pairs > 0 else 1.0 