import torch
import copy
from models.tinybert_lora import TinyBERTLoRA
from tqdm import tqdm
from core.loss import CrossEntropyWithPenalty

class Client:
    def __init__(self, client_id, config, role='train', reputation=0):
        self.client_id = client_id
        self.config = config
        self.role = role  # train/committee/idle
        self.reputation = reputation
        self.credit = 0.0  # 新增：信誉分
        self.si = 0        # 新增：表现良好次数
        self.pi = 0        # 新增：表现差次数
        self.role_history = []  # 新增：最近三轮角色历史
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = TinyBERTLoRA(
            model_name=config['model']['client_model_type'],
            lora_r=config['model']['lora_r'],
            lora_alpha=config['model']['lora_alpha'],
            lora_dropout=config['model']['lora_dropout']
        ).to(self.device)

    def download_lora(self, lora_state_dict):
        self.model.set_lora_parameters(lora_state_dict)

    def local_train(self, dataloader, epochs=None, lr=None):
        self.model.train()
        if epochs is None:
            epochs = int(self.config['model'].get('local_epochs', 1))
        if lr is None:
            lr = float(self.config['model'].get('learning_rate', 2e-4))
        lora_lr_factor = float(self.config['model'].get('lora_lr_factor', 0.1))
        classifier_lr_factor = float(self.config['model'].get('classifier_lr_factor', 1.0))
        weight_decay = float(self.config['model'].get('weight_decay', 0.01))
        clip_grad_norm = float(self.config['model'].get('clip_grad_norm', 1.0))
        use_lr_scheduler = self.config['model'].get('use_lr_scheduler', False)
        lora_params = [p for n, p in self.model.named_parameters() if 'lora_' in n]
        classifier_params = [p for n, p in self.model.named_parameters() if 'classifier' in n]
        optimizer = torch.optim.AdamW([
            {'params': lora_params, 'lr': lr * lora_lr_factor, 'weight_decay': weight_decay},
            {'params': classifier_params, 'lr': lr * classifier_lr_factor, 'weight_decay': weight_decay * 2}
        ])
        scheduler = None
        if use_lr_scheduler:
            scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=1, gamma=0.9)
        
        # 从配置中实例化我们新的、统一的损失函数
        loss_config = self.config.get('loss', {})
        criterion = CrossEntropyWithPenalty(
            pos_weight=float(loss_config.get('pos_weight', 1.0)),
            lambda_penalty=float(loss_config.get('lambda_penalty', 0.0)),
            gamma_margin=float(loss_config.get('gamma_margin', 0.0))
        )
        print(f"Client {self.client_id} 使用损失函数: CrossEntropyWithPenalty (pos_weight={criterion.pos_weight}, lambda={criterion.lambda_penalty}, gamma={criterion.gamma_margin})")

        for epoch in tqdm(range(epochs), desc=f"Client {self.client_id} 本地训练进度", leave=True):
            with tqdm(dataloader, desc=f"Epoch {epoch+1}/{epochs}", leave=False) as batch_bar:
                for batch in batch_bar:
                    optimizer.zero_grad()
                    input_ids = batch['input_ids'].to(self.device)
                    attention_mask = batch['attention_mask'].to(self.device)
                    labels = batch['labels'].to(self.device)
                    logits = self.model(input_ids, attention_mask)
                    
                    # 使用新的损失函数
                    loss = criterion(logits, labels.squeeze(-1))
                    loss.backward()
                    if clip_grad_norm > 0:
                        torch.nn.utils.clip_grad_norm_(
                            [p for n, p in self.model.named_parameters() if 'lora_' in n or 'classifier' in n], 
                            max_norm=clip_grad_norm
                        )
                    optimizer.step()
                    total_norm = 0.0
                    for p in self.model.parameters():
                        if p.grad is not None:
                            param_norm = p.grad.data.norm(2)
                            total_norm += param_norm.item() ** 2
                    total_norm = total_norm ** 0.5
                    batch_bar.set_postfix(loss=loss.item(), grad_norm=total_norm)
            if scheduler is not None:
                scheduler.step()
            # 每轮训练后主动清理缓存，防止内存泄漏
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            import gc
            gc.collect()
        print(f"Client {self.client_id} 训练完成，基础学习率: {lr}, LoRA学习率: {lr * lora_lr_factor}, 分类头学习率: {lr * classifier_lr_factor}, 本地训练轮数: {epochs}")
        return self.model.get_lora_parameters()

    def upload_update(self):
        return self.model.get_lora_parameters()

    def validate_update(self, lora_state_dict, val_dataloader, global_lora_state, global_f1_baseline=0.0, threshold=None):
        """
        验证一个LoRA更新
        
        Args:
            lora_state_dict: 要验证的LoRA参数
            val_dataloader: 验证集加载器
            global_lora_state: 全局LoRA参数，用于计算余弦距离
            global_f1_baseline: 基准F1分数，用于计算F1变化
            threshold: 正类分类阈值，如果为None则使用配置中的默认值
            
        Returns:
            f1_delta: F1变化
            dist: 与全局模型的余弦距离
            metrics: 评估指标
            global_f1_baseline: 输入的基准F1
            new_f1: 更新后的F1
        """
        from utils.metrics import cosine_distance
        
        # 如果没有提供阈值，从配置中获取默认值
        if threshold is None:
            threshold = float(self.config['model'].get('classification_threshold', 0.5))
            
        old_lora = self.model.get_lora_parameters()
        self.model.set_lora_parameters(lora_state_dict)
        metrics = self.model.evaluate(val_dataloader, self.device, threshold=threshold)
        new_f1 = metrics['f1']
        f1_delta = new_f1 - global_f1_baseline
        dist = cosine_distance(self.model.get_lora_parameters(), global_lora_state)
        self.model.set_lora_parameters(old_lora)
        return f1_delta, dist, metrics, global_f1_baseline, new_f1

    def update_reputation(self, delta):
        self.reputation += delta 

    def update_credit(self, delta):
        self.credit += delta
        self.credit = max(-5, min(5, self.credit))

    def update_si(self, delta=1):
        self.si += delta

    def update_pi(self, delta=1):
        self.pi += delta

    def update_role_history(self, role):
        self.role_history.append(role)
        if len(self.role_history) > 3:
            self.role_history.pop(0)

    def get_recent_roles(self, n=3):
        return self.role_history[-n:] if len(self.role_history) >= n else self.role_history[:]

    def apply_log_credit_formula(self, alpha=1.0, beta=1.2):
        import math
        score = alpha * math.log(1 + self.si) - beta * math.log(1 + self.pi)
        self.credit = max(-5, min(5, score)) 