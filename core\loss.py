import torch
import torch.nn as nn
import torch.nn.functional as F

class CrossEntropyWithPenalty(nn.Module):
    """
    一个自定义的交叉熵损失函数，包含类别权重和对正类的logit惩罚。
    - 解决了样本不平衡问题（通过加权）。
    - 增加了对正类预测的logit惩罚，促使模型对欺诈样本的判断更"确信"。
    """
    def __init__(self, pos_weight, lambda_penalty, gamma_margin):
        """
        初始化损失函数。
        Args:
            pos_weight (float): 正类的权重。
            lambda_penalty (float): logit惩罚项的系数。
            gamma_margin (float): logit惩罚的"确信度"边际阈值。
        """
        super().__init__()
        self.pos_weight = pos_weight
        self.lambda_penalty = lambda_penalty
        self.gamma_margin = gamma_margin
        # 创建一个标准的BCEWithLogitsLoss，但我们会手动应用权重
        self.bce_loss = nn.BCEWithLogitsLoss(reduction='none')

    def forward(self, logits, labels):
        """
        计算总损失。
        Args:
            logits (torch.Tensor): 模型的原始logit输出。
            labels (torch.Tensor): 真实标签。
        Returns:
            torch.Tensor: 计算出的总损失。
        """
        # 修正：确保logits和labels的维度匹配BCEWithLogitsLoss的要求
        if logits.ndim == labels.ndim + 1 and logits.shape[-1] == 1:
            logits = logits.squeeze(-1)

        # 1. 计算加权交叉熵损失
        # 创建与label同形状的权重张量
        weights = torch.ones_like(labels, dtype=torch.float)
        weights[labels == 1] = self.pos_weight
        
        # 计算基础BCE损失
        unweighted_loss = self.bce_loss(logits, labels.float())
        
        # 应用权重
        weighted_bce_loss = (unweighted_loss * weights).mean()

        # 2. 计算正类logit惩罚
        # 只对正类样本(label==1)进行计算
        positive_logits = logits[labels == 1]
        
        # 如果没有正类样本，则惩罚为0
        if positive_logits.numel() > 0:
            # max(0, gamma - z_i)
            penalty_per_sample = F.relu(self.gamma_margin - positive_logits)
            penalty = self.lambda_penalty * penalty_per_sample.mean()
        else:
            penalty = 0.0

        # 3. 计算总损失
        total_loss = weighted_bce_loss + penalty
        
        return total_loss 