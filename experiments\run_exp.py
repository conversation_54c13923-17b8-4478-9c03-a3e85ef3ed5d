import yaml
import os
import random
import numpy as np
import torch
from core.server import Server
from core.client import Client
from train_rounds.round_runner import RoundRunner
from data.dataset_loader import get_dataloader
from utils.visualizer import plot_metrics
from tqdm import tqdm

def set_seed(seed):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)

def main(config_path):
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    set_seed(config['global']['seed'])
    # 初始化数据
    num_clients = config['federated']['num_clients']
    train_path = config['data']['train_path']
    val_path = config['data']['val_path']
    test_path = config['data']['test_path']
    tokenizer_name = config['model']['client_model_type']
    batch_size = config['data']['batch_size']
    max_length = config['data']['max_seq_length']
    # 模拟数据划分
    from data.dataset_loader import FraudTextDataset
    full_train = FraudTextDataset(train_path, tokenizer_name, max_length)
    full_val = FraudTextDataset(val_path, tokenizer_name, max_length)
    test_loader = get_dataloader(test_path, tokenizer_name, batch_size, max_length, shuffle=False)
    
    # 显示数据集信息
    print(f"\n{'='*50}")
    print(f"数据集信息:")
    print(f"训练集: {len(full_train)} 样本")
    print(f"验证集: {len(full_val)} 样本") 
    print(f"测试集: {len(test_loader.dataset)} 样本")
    print(f"{'='*50}\n")
    
    # 平均划分
    train_indices = np.array_split(np.arange(len(full_train)), num_clients)
    val_indices = np.array_split(np.arange(len(full_val)), num_clients)
    train_loaders = {}
    val_loaders = {}
    for i in range(num_clients):
        train_subset = torch.utils.data.Subset(full_train, train_indices[i])
        val_subset = torch.utils.data.Subset(full_val, val_indices[i])
        train_loaders[i] = torch.utils.data.DataLoader(train_subset, batch_size=batch_size, shuffle=True)
        val_loaders[i] = torch.utils.data.DataLoader(val_subset, batch_size=batch_size, shuffle=False)
    # 初始化客户端
    print(f"初始化 {num_clients} 个客户端...")
    clients = [Client(i, config) for i in range(num_clients)]
    # 初始化服务器
    print(f"初始化中央服务器...")
    server = Server(config)
    # 训练轮数
    rounds = config['global']['rounds']
    runner = RoundRunner(server, clients, config, train_loaders, val_loaders, test_loader)
    # history字典现在将在循环中动态构建
    history = {}
    
    print(f"\n开始联邦训练，共 {rounds} 轮")
    for r in range(1, rounds+1):
        print(f"\n{'-'*20} Round {r}/{rounds} {'-'*20}")
        log = runner.run_round(r)
        metrics = log['global_model_metrics']
        
        # 动态地将所有数值型指标添加到history中
        for k, v in metrics.items():
            if isinstance(v, (int, float)): # 只记录数值型指标
                if k not in history:
                    history[k] = []
                history[k].append(v)

        print(f"Global Metrics | acc={metrics['accuracy']:.4f} f1={metrics['f1']:.4f} loss={metrics['loss']:.4f} prec={metrics['precision']:.4f} rec={metrics['recall']:.4f}")
        print(f"{'-'*50}\n")
    # 可视化
    print("训练完成，生成指标图表...")
    plot_metrics(history, save_path=os.path.join(config['log']['log_dir'], 'metrics.png'))
    print(f"指标图表已保存至 {config['log']['log_dir']}/metrics.png")

if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--config', type=str, default='config/config.yaml')
    args = parser.parse_args()
    main(args.config) 