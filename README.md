基于委员会共识与激励机制的联邦学习文本欺诈检测平台
====================================================================

本项目实现了一个面向文本欺诈检测的高效联邦学习平台，集成了LoRA参数高效通信、分类头联合训练、动态激励与信誉系统、委员会共识机制、灵活配置与可视化追踪等多项创新设计，适用于大规模分布式文本分类、金融风控等场景。

## 目录结构与模块说明

### 1. 配置与数据
**config/**
- `config.yaml`：主配置文件，支持模型、联邦、激励、数据、日志等参数灵活调整，包含阶段性参数表、F1阈值设置等高级功能。

**data/**
- `dataset_loader.py`：数据加载与预处理，支持JSON/JSONL格式，实现了自动标签处理与分词，自定义Dataset类。
- `train.json`/`eval.json`/`test.json`：标准数据集，格式为文本+标签。

### 2. 模型与算法
**models/**
- `tinybert_lora.py`：核心模型，基于TinyBERT（4层/312维），集成以下特性：
  - LoRA低秩参数高效微调：仅在Attention层注入可训练参数，显著减少通信开销
  - 分类头与LoRA参数联合训练与同步，保证模型全局一致性
  - 灵活的评估方法：支持自定义阈值分类、混淆矩阵、AUC计算
  - 概率统计分析：自动收集预测分布统计信息，方便后续分析
- `base_model.py`：模型抽象基类，定义LoRA参数获取/设置、前向传播、评估等通用接口，便于扩展其他模型结构。

### 3. 联邦学习核心流程
**core/**
- `server.py`：中央服务器实现：
  - 全局模型管理：模型版本控制、参数下发与聚合
  - 阶段性F1阈值控制：支持不同训练阶段动态调整回滚标准
  - 回滚机制：当全局模型性能下降时自动回滚到上一版本
  - 日志记录：自动保存每轮训练详细日志
- `client.py`：客户端实现：
  - 本地训练：支持多轮训练、学习率调整、梯度裁剪
  - 参数管理：上传LoRA+分类头参数，减少通信负担
  - 创新损失函数：实现`CrossEntropyWithPenalty`交叉熵+正类惩罚损失，通过对正类logit施加惩罚，提高精确率
  - 信誉与表现跟踪：记录客户端信誉变化、角色历史、表现统计
- `committee.py`：委员会共识机制：
  - 多委员协作验证：每轮随机选择多个委员验证上传的模型参数
  - 阶段性验证参数表：根据训练阶段动态调整α(F1权重)、β(距离权重)、θ(通过阈值)、γ(距离阈值)
  - F1基准动态计算：使用公共验证集D_shared确保评估一致性
  - 强制拒绝机制：当F1下降超过阈值时强制拒绝更新，保障系统稳定性
- `aggregator.py`：多种参数聚合算法：
  - 标准FedAvg聚合：等权重平均所有客户端参数
  - 信誉加权聚合：根据客户端信誉值加权平均参数，偏向高信誉客户端
- `selector.py`：客户端角色分配策略：
  - 信誉排序选择：高信誉客户端优先作为委员会成员
  - 轮盘赌选择：基于信誉值的概率选择，平衡探索与利用
  - 角色轮换促进：考虑历史角色，防止单一客户端长期占据同一角色

### 4. 训练与实验流程
**train_rounds/**
- `round_runner.py`：单轮联邦训练主流程的复杂实现：
  - 客户端角色动态分配：每轮根据信誉和历史角色分配委员会/训练/空闲角色
  - 验证集策略性分割：公共数据集D_shared + 私有数据集D_i，带重叠比例控制
  - 全流程指标追踪：记录每个客户端的训练时间、上传大小、表现指标等
  - 投票机制实现：支持多数表决、加权表决等，可自定义共识比例
  - 信誉更新逻辑：根据模型表现、投票行为等动态调整信誉值
  - 异常情况处理：性能下降检测、回滚触发等

### 5. 工具与可视化
**utils/**
- `logger.py`：日志工具，支持多级日志输出、自动保存、格式化等。
- `metrics.py`：指标计算工具，实现余弦距离、参数差异计算等高级指标。
- `visualizer.py`：训练过程与结果可视化，支持指标曲线、激励分布、混淆矩阵等多种可视化图表。

### 6. 其他
- `logs/`：每轮训练日志、指标曲线自动保存。
- `outputs/`：实验报告、可视化图片输出目录。
- `experiments/`：主程序入口（如run_exp.py），加载配置并启动实验。

## 主要模块设计与创新方法

### 1. LoRA+分类头联合训练
**创新点**：
- 首次在联邦学习中同时同步LoRA参数和分类头参数，保证模型全局一致性
- 差异化学习率：LoRA参数与分类头使用不同学习率优化，提升训练效率

**技术实现**：
- 在`tinybert_lora.py`中，`get_lora_parameters()`和`set_lora_parameters()`方法同时处理LoRA和分类头参数
- 客户端训练时对不同组参数应用不同学习率：`optimizer = torch.optim.AdamW([{'params': lora_params, 'lr': lr * lora_lr_factor}, {'params': classifier_params, 'lr': lr * classifier_lr_factor}])`
- 通过参数尺寸计算(`_calculate_params_size()`)，仅传输必要参数，大幅减少通信开销

### 2. 委员会共识机制
**创新点**：
- 每轮动态选择委员会成员，使用多维度评估标准验证模型更新
- 阶段性参数表：训练不同阶段采用不同验证标准，平衡探索与利用

**技术实现**：
- 通过`get_phase_params()`方法动态获取当前轮次的α(F1权重)、β(距离权重)等参数
- 多委员独立验证计算评分：`score = alpha * f1_delta + beta * (1 - dist)`
- 强制拒绝机制：`if f1_delta < committee_f1_threshold: pass_score = False`
- 共识比例阈值：只有超过共识比例的更新才会被采纳

### 3. 激励与信誉系统
**创新点**：
- 多维信誉分更新：基于客户端表现、投票行为、角色历史等综合评估
- 对数信用公式：`score = alpha * math.log(1 + self.si) - beta * math.log(1 + self.pi)`
- 角色轮换促进：分析最近N轮角色历史，奖励多角色参与

**技术实现**：
- `update_reputation()`、`update_credit()`等方法处理信誉值更新
- `update_role_history()`记录角色历史，用于后续分析
- 信誉值用于角色分配(`select_by_credit()`)和参数聚合(`credit_weighted_agg()`)

### 4. "交叉熵+正类惩罚"损失与自定义阈值
**创新点**：
- 对正类logit施加惩罚，防止模型过于自信地预测正类，提升精确率
- 推理时支持自定义阈值，灵活调整精度/召回平衡

**技术实现**：
- 自定义损失函数：`CrossEntropyWithPenalty`类实现
- 惩罚机制：`logits_adjusted[:, 1] = logits_adjusted[:, 1] - self.penalty_weight`
- 评估时支持阈值：`batch_preds = (probs[:, 1] >= threshold).long()`

### 5. 回滚与异常检测
**创新点**：
- 全局模型性能显著下降时自动回滚，确保系统稳定性
- 阶段性回滚阈值：不同训练阶段使用不同的F1下降容忍度

**技术实现**：
- 通过`get_global_f1_threshold()`动态获取当前轮次的回滚阈值
- 回滚触发条件：`if delta < f1_rollback_threshold: self.model.set_lora_parameters(self.last_lora_state)`
- 保存每轮参数状态：`self.last_lora_state = copy.deepcopy(self.model.get_lora_parameters())`

### 6. 灵活配置与可视化
**创新点**：
- 所有机制均可通过配置文件灵活开关，无需修改代码
- 训练过程完整可视化与追踪，便于实验分析

**技术实现**：
- 配置项与代码分离：所有参数从`config.yaml`读取
- 完整日志记录：每轮训练详细记录客户端选择、训练时间、上传大小、评估结果等
- 结果自动可视化：生成精确率、召回率、F1值等曲线图表

## 快速开始

### 安装依赖：
```bash
pip install -r requirements.txt
```

### 配置实验参数：
编辑 `config/config.yaml`，可灵活调整模型、联邦、激励、数据等参数。主要参数包括：
- 模型配置：预训练模型选择、LoRA参数设置、分类阈值等
- 联邦配置：客户端数量、委员会数量、训练客户端数量等
- 激励配置：信誉初始值、更新系数、角色轮换策略等
- 数据配置：数据路径、批量大小、序列长度等

### 运行主程序：
```bash
python -m experiments.run_exp --config config/config.yaml
```

## 日志与可视化
- 每轮训练日志、指标、激励分布等自动保存在 `logs/` 目录。
- 训练指标曲线、激励分布等可视化图片输出到 `outputs/plots/`。
- 支持实验报告自动生成（`outputs/reports/`）。
分析脚本
python experiments/analyze_results.py

## 常见问题与建议
- **如何切换模型或机制？**
  修改 `config/config.yaml` 中相关参数即可，无需改动代码。
- **如何自定义激励或聚合策略？**
  可在 `core/committee.py`、`core/aggregator.py` 等文件自定义实现。
- **训练效果不佳怎么办？**
  - 检查数据分布、标签比例，适当调整学习率、批量大小、训练轮数等参数。
  - 尝试调整正类惩罚权重`penalty_weight`和分类阈值`threshold`。
  - 可先单机训练模型，确保收敛后再进行联邦实验。
- **日志/可视化未输出？**
  检查 `log_dir`、`outputs/` 目录权限，或查看 `utils/` 下相关工具实现。
