import torch

def fedavg(client_lora_list):
    """标准FedAvg聚合LoRA参数"""
    if not client_lora_list:
        return None
    agg = {}
    for k in client_lora_list[0].keys():
        agg[k] = torch.stack([c[k] for c in client_lora_list], 0).mean(0)
    return agg

def credit_weighted_agg(client_lora_list, client_credits):
    """基于信誉分的加权聚合LoRA参数"""
    if not client_lora_list:
        return None
    total_credit = sum(client_credits)
    agg = {}
    for k in client_lora_list[0].keys():
        weighted = sum(c[k] * w for c, w in zip(client_lora_list, client_credits))
        agg[k] = weighted / total_credit if total_credit > 0 else torch.stack([c[k] for c in client_lora_list], 0).mean(0)
    return agg

def aggregate(client_lora_list, strategy='fedavg', client_credits=None):
    if strategy == 'fedavg' or client_credits is None:
        return fedavg(client_lora_list)
    elif strategy == 'credit':
        return credit_weighted_agg(client_lora_list, client_credits)
    else:
        raise ValueError(f'未知聚合策略: {strategy}') 