import torch
import torch.nn as nn
from abc import ABC, abstractmethod

class BaseModel(nn.Module, ABC):
    """
    所有模型的抽象基类，定义必要接口。
    """
    def __init__(self):
        super().__init__()

    @abstractmethod
    def forward(self, input_ids, attention_mask=None, **kwargs):
        pass

    @abstractmethod
    def get_lora_parameters(self):
        """返回LoRA层参数（用于联邦学习上传）"""
        pass

    @abstractmethod
    def set_lora_parameters(self, lora_state_dict):
        """加载LoRA层参数（用于联邦学习下发）"""
        pass

    @abstractmethod
    def evaluate(self, dataloader, device):
        """评估模型，返回指标字典"""
        pass 