import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

def visualize_comparison_results():
    """
    生成并保存对比实验结果的可视化图表。
    图表1: 分类性能指标 (ACC, F1, RECALL, AUC) 的分组柱状图。
    图表2: 模型参数量的柱状图 (使用对数尺度)。
    """
    # --- 1. 数据准备 ---
    # 根据您提供的"需求文档"手动解析数据
    # 分类性能数据
    perf_data = {
        'Model': ['FedAvg', 'FedProx', 'FedAdam', 'Fed-CIC (OUR)'],
        'ACC': [0.8050, 0.8195, 0.8122, 0.8456],
        'ACC_err': [0.005, 0.002, 0.004, 0.003],
        'F1': [0.8388, 0.8325, 0.8286, 0.8625],
        'F1_err': [0.006, 0.003, 0.004, 0.002],
        'RECALL': [0.9479, 0.9733, 0.9583, 0.9596],
        'RECALL_err': [0.001, 0.002, 0.001, 0.003],
        'AUC': [0.8101, 0.8819, 0.8092, 0.9229],
        'AUC_err': [0.003, 0.004, 0.003, 0.002]
    }
    perf_df = pd.DataFrame(perf_data)

    # 参数量数据
    params_data = {
        'Model': ['FedAvg', 'FedProx', 'FedAdam', 'Fed-CIC (OUR)'],
        '参数量 (KB)': [56058.1016, 56058.1016, 56058.1016, 314.4453]
    }
    params_df = pd.DataFrame(params_data)

    # 为分组柱状图重塑数据结构
    perf_df_melted = perf_df.melt(
        id_vars='Model',
        value_vars=['ACC', 'F1', 'RECALL', 'AUC'],
        var_name='Metric',
        value_name='Score'
    )
    err_df_melted = perf_df.melt(
        id_vars='Model',
        value_vars=['ACC_err', 'F1_err', 'RECALL_err', 'AUC_err'],
        var_name='Metric_err_name',
        value_name='Error'
    )
    err_df_melted['Metric'] = err_df_melted['Metric_err_name'].str.replace('_err', '')
    perf_df_melted = pd.merge(perf_df_melted, err_df_melted[['Model', 'Metric', 'Error']], on=['Model', 'Metric'])

    # --- 2. 绘图设置 ---
    # 设置绘图风格以达到出版物质量
    sns.set_theme(style="whitegrid")
    # 设置支持中文的字体，例如 'SimHei', 'Microsoft YaHei'
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    # --- 3. 绘制图表1: 分类性能对比 ---
    plt.figure(figsize=(12, 7))
    ax1 = sns.barplot(
        data=perf_df_melted,
        x='Metric',
        y='Score',
        hue='Model',
        palette='viridis' # 使用一个视觉友好的色板
    )

    # 为分组柱状图添加误差棒的修复方法
    # 通过迭代hue和x的类别来精确控制误差棒的位置
    hue_order = perf_df['Model'].unique()
    x_ticks = ax1.get_xticks()
    for i, metric in enumerate(perf_df_melted['Metric'].unique()):
        for j, model in enumerate(hue_order):
            # 获取特定组的数据点
            point_data = perf_df_melted[(perf_df_melted['Metric'] == metric) & (perf_df_melted['Model'] == model)]
            if point_data.empty:
                continue

            score = point_data['Score'].iloc[0]
            error = point_data['Error'].iloc[0]

            # 计算该组柱子的中心x坐标
            # 这是基于seaborn分组柱状图的内部偏移逻辑
            group_width = 0.8 # seaborn中dodge=True时默认的总宽度
            bar_width = group_width / len(hue_order)
            x_pos = x_ticks[i] - group_width / 2 + bar_width / 2 + j * bar_width

            ax1.errorbar(x=x_pos, y=score, yerr=error, fmt='none', c='black', capsize=3)

    ax1.set_title('不同联邦学习算法性能对比', fontsize=16, fontweight='bold')
    ax1.set_xlabel('评价指标', fontsize=12)
    ax1.set_ylabel('分数', fontsize=12)
    ax1.set_ylim(0.75, 1.0)  # 调整Y轴范围以突出差异
    ax1.legend(title='算法', fontsize=10)
    plt.tight_layout()
    plt.savefig('comparison_performance.png', dpi=300, bbox_inches='tight')
    print("性能对比图已成功保存为 'comparison_performance.png'")

    # --- 4. 绘制图表2: 参数量对比 ---
    plt.figure(figsize=(8, 6))
    ax2 = sns.barplot(
        data=params_df,
        x='Model',
        y='参数量 (KB)',
        palette='plasma' # 使用另一个色板
    )
    ax2.set_yscale('log') # **关键：使用对数尺度**
    ax2.set_title('不同算法的参数量对比 (对数尺度)', fontsize=16, fontweight='bold')
    ax2.set_xlabel('算法', fontsize=12)
    ax2.set_ylabel('参数量 (KB) - 对数尺度', fontsize=12)
    plt.xticks(rotation=15)

    # 在柱状图上添加数值标签
    for p in ax2.patches:
        ax2.annotate(f"{p.get_height():.2f}",
                       (p.get_x() + p.get_width() / 2., p.get_height()),
                       ha='center', va='center',
                       xytext=(0, 9),
                       textcoords='offset points',
                       fontsize=10)

    plt.tight_layout()
    plt.savefig('comparison_params.png', dpi=300, bbox_inches='tight')
    print("参数量对比图已成功保存为 'comparison_params.png'")


if __name__ == '__main__':
    visualize_comparison_results()
    plt.show() # 在运行脚本时显示图像 