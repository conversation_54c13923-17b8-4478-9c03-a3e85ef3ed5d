import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

def visualize_ablation_results():
    """
    生成并保存消融实验结果的可视化图表。
    图表1: 分类性能指标 (ACC, F1, RECALL, AUC) 的分组柱状图。
    图表2: 模型参数量的柱状图 (使用对数尺度)。
    """
    # --- 1. 数据准备 ---
    # 根据您提供的消融实验数据
    perf_data = {
        'Model': ['无激励', '无委员会', '无LoRA（全参数）', 'Fed-CIC (OUR)'],
        'ACC': [0.8192, 0.8053, 0.8551, 0.8456],
        'ACC_err': [0.001, 0.005, 0.004, 0.003],
        'F1': [0.8383, 0.8330, 0.8582, 0.8625],
        'F1_err': [0.001, 0.004, 0.004, 0.002],
        'RECALL': [0.9239, 0.9180, 0.8641, 0.9596],
        'RECALL_err': [0.003, 0.006, 0.004, 0.003],
        'AUC': [0.8962, 0.8910, 0.9287, 0.9229],
        'AUC_err': [0.001, 0.003, 0.002, 0.002]
    }
    perf_df = pd.DataFrame(perf_data)

    params_data = {
        'Model': ['无激励', '无委员会', '无LoRA（全参数）', 'Fed-CIC (OUR)'],
        '参数量 (KB)': [314.4453, 314.4453, 56058.1016, 314.4453]
    }
    params_df = pd.DataFrame(params_data)

    # 为分组柱状图重塑数据结构
    perf_df_melted = perf_df.melt(id_vars='Model', value_vars=['ACC', 'F1', 'RECALL', 'AUC'], var_name='Metric', value_name='Score')
    err_df_melted = perf_df.melt(id_vars='Model', value_vars=['ACC_err', 'F1_err', 'RECALL_err', 'AUC_err'], var_name='Metric_err_name', value_name='Error')
    err_df_melted['Metric'] = err_df_melted['Metric_err_name'].str.replace('_err', '')
    perf_df_melted = pd.merge(perf_df_melted, err_df_melted[['Model', 'Metric', 'Error']], on=['Model', 'Metric'])

    # --- 2. 绘图设置 ---
    sns.set_theme(style="whitegrid")
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    # --- 3. 绘制图表1: 分类性能对比 ---
    plt.figure(figsize=(12, 7))
    ax1 = sns.barplot(data=perf_df_melted, x='Metric', y='Score', hue='Model', palette='viridis')

    hue_order = perf_df['Model'].unique()
    x_ticks = ax1.get_xticks()
    for i, metric in enumerate(perf_df_melted['Metric'].unique()):
        for j, model in enumerate(hue_order):
            point_data = perf_df_melted[(perf_df_melted['Metric'] == metric) & (perf_df_melted['Model'] == model)]
            if point_data.empty: continue
            score = point_data['Score'].iloc[0]
            error = point_data['Error'].iloc[0]
            group_width = 0.8
            bar_width = group_width / len(hue_order)
            x_pos = x_ticks[i] - group_width / 2 + bar_width / 2 + j * bar_width
            ax1.errorbar(x=x_pos, y=score, yerr=error, fmt='none', c='black', capsize=3)

    ax1.set_title('消融实验性能对比', fontsize=16, fontweight='bold')
    ax1.set_xlabel('评价指标', fontsize=12)
    ax1.set_ylabel('分数', fontsize=12)
    ax1.set_ylim(0.80, 1.0)
    ax1.legend(title='实验配置', fontsize=10)
    plt.tight_layout()
    plt.savefig('ablation_performance.png', dpi=300, bbox_inches='tight')
    print("消融实验性能图已成功保存为 'ablation_performance.png'")

    # --- 4. 绘制图表2: 参数量对比 ---
    plt.figure(figsize=(8, 6))
    ax2 = sns.barplot(data=params_df, x='Model', y='参数量 (KB)', palette='plasma', hue='Model', legend=False)
    ax2.set_yscale('log')
    ax2.set_title('消融实验参数量对比 (对数尺度)', fontsize=16, fontweight='bold')
    ax2.set_xlabel('实验配置', fontsize=12)
    ax2.set_ylabel('参数量 (KB) - 对数尺度', fontsize=12)
    plt.xticks(rotation=15, ha='right')

    for p in ax2.patches:
        ax2.annotate(f"{p.get_height():.2f}", (p.get_x() + p.get_width() / 2., p.get_height()), ha='center', va='center', xytext=(0, 9), textcoords='offset points', fontsize=10)

    plt.tight_layout()
    plt.savefig('ablation_params.png', dpi=300, bbox_inches='tight')
    print("消融实验参数量图已成功保存为 'ablation_params.png'")

if __name__ == '__main__':
    visualize_ablation_results()
    plt.show() 