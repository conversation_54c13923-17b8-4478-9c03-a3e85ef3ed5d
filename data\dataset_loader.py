import json
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer

class FraudTextDataset(Dataset):
    def __init__(self, data_path, tokenizer_name, max_length=256):
        self.samples = []
        self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
        self.max_length = max_length
        with open(data_path, 'r', encoding='utf-8') as f:
            if data_path.endswith('.jsonl'):
                for line in f:
                    self.samples.append(json.loads(line))
            else:
                self.samples = json.load(f)

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        sample = self.samples[idx]
        text = sample['input']
        # 标签处理：is_fraud -> 0/1
        label_json = sample['output']
        if isinstance(label_json, str):
            label_json = json.loads(label_json)
        label = int(label_json['is_fraud'])
        encoding = self.tokenizer(
            text,
            truncation=True,
            max_length=self.max_length,
            padding='max_length',
            return_tensors='pt'
        )
        return {
            'input_ids': encoding['input_ids'].squeeze(0),
            'attention_mask': encoding['attention_mask'].squeeze(0),
            'labels': torch.tensor(label, dtype=torch.long)
        }

def get_dataloader(data_path, tokenizer_name, batch_size=16, max_length=256, shuffle=True):
    dataset = FraudTextDataset(data_path, tokenizer_name, max_length)
    return DataLoader(dataset, batch_size=batch_size, shuffle=shuffle) 