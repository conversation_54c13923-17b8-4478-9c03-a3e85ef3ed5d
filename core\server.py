import os
import copy
import torch
import yaml
from models.tinybert_lora import TinyBERTLoRA
from utils.logger import get_logger
from utils.metrics import compute_metrics
from data.dataset_loader import FraudTextDataset

class Server:
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = TinyBERTLoRA(
            model_name=config['model']['client_model_type'],
            lora_r=config['model']['lora_r'],
            lora_alpha=config['model']['lora_alpha'],
            lora_dropout=config['model']['lora_dropout']
        ).to(self.device)
        self.global_model_version = 0
        self.log_dir = config['log']['log_dir']
        os.makedirs(self.log_dir, exist_ok=True)
        self.last_f1 = None
        self.last_lora_state = None
        # 加载全局数据集，供每轮动态分配
        self.train_dataset = FraudTextDataset(config['data']['train_path'], config['model']['client_model_type'], config['data']['max_seq_length'])
        self.val_dataset = FraudTextDataset(config['data']['val_path'], config['model']['client_model_type'], config['data']['max_seq_length'])

    def get_global_f1_threshold(self, round_idx):
        """
        根据当前轮次获取全局F1回滚阈值
        """
        # 优先从配置的阶段性阈值中获取
        phase_thresholds = self.config['federated'].get('phase_f1_thresholds', None)
        if phase_thresholds is not None:
            for phase in phase_thresholds:
                if phase['start'] <= round_idx <= phase['end']:
                    return phase['global']
        
        # 如果没有阶段性配置，使用默认全局阈值
        return self.config['federated'].get('f1_rollback_threshold', -0.03)

    def broadcast_lora(self):
        """下发当前全局LoRA参数"""
        return copy.deepcopy(self.model.get_lora_parameters())

    def aggregate(self, client_lora_list, weights=None):
        """聚合客户端上传的LoRA参数"""
        if not client_lora_list:
            print('没有可聚合的客户端LoRA参数，保持上一轮模型')
            return
        # 简单FedAvg
        agg_lora = {}
        for k in client_lora_list[0].keys():
            agg_lora[k] = torch.stack([c[k] for c in client_lora_list], 0).mean(0)
        self.model.set_lora_parameters(agg_lora)
        self.global_model_version += 1

    def evaluate(self, dataloader):
        self.model.eval()
        return self.model.evaluate(dataloader, self.device)

    def maybe_rollback(self, metrics, round_idx):
        # 获取当前轮次的全局F1回滚阈值
        f1_rollback_threshold = self.get_global_f1_threshold(round_idx)
        print(f"\n当前轮次{round_idx}的全局F1回滚阈值: {f1_rollback_threshold}")
        
        # F1回滚机制
        if self.last_f1 is not None:
            delta = metrics['f1'] - self.last_f1
            if delta < f1_rollback_threshold:
                print(f'F1下降{delta:.4f}，超过阈值{f1_rollback_threshold}，回滚到上一轮LoRA参数')
                self.model.set_lora_parameters(self.last_lora_state)
                return True
        self.last_f1 = metrics['f1']
        self.last_lora_state = copy.deepcopy(self.model.get_lora_parameters())
        return False

    def save_round_log(self, round_log):
        import json
        log_path = os.path.join(self.log_dir, f'round_{round_log["round"]:03d}.json')
        with open(log_path, 'w', encoding='utf-8') as f:
            json.dump(round_log, f, ensure_ascii=False, indent=2)
        # 追加到总日志文件
        total_log_path = os.path.join(self.log_dir, 'round_logs.json')
        if os.path.exists(total_log_path):
            with open(total_log_path, 'r', encoding='utf-8') as f:
                all_logs = json.load(f)
        else:
            all_logs = []
        all_logs.append(round_log)
        with open(total_log_path, 'w', encoding='utf-8') as f:
            json.dump(all_logs, f, ensure_ascii=False, indent=2) 