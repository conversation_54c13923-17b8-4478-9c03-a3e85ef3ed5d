#!/usr/bin/env python3
"""
测试Non-IID数据分配功能
"""

import yaml
import json
import numpy as np
from utils.non_iid_splitter import NonIIDDataSplitter

class MockDataset:
    """模拟数据集用于测试"""
    def __init__(self, data_path):
        self.samples = []
        with open(data_path, 'r', encoding='utf-8') as f:
            self.samples = json.load(f)

    def __len__(self):
        return len(self.samples)

def test_non_iid_split():
    """测试Non-IID数据分配"""

    # 加载配置
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)

    print("=== Non-IID数据分配测试 ===")
    print(f"配置信息:")
    print(f"  分布类型: {config['data']['distribution_type']}")
    print(f"  Alpha参数: {config['data']['non_iid_alpha']}")
    print(f"  最少样本数: {config['data']['min_samples_per_client']}")
    print(f"  客户端数量: {config['federated']['num_clients']}")

    # 加载数据集
    print("\n加载训练数据集...")
    train_dataset = MockDataset(config['data']['train_path'])

    print(f"训练集总样本数: {len(train_dataset)}")

    # 统计原始标签分布
    labels = []
    for sample in train_dataset.samples:
        label_json = sample['output']
        if isinstance(label_json, str):
            label_json = json.loads(label_json)
        label = int(label_json['is_fraud'])
        labels.append(label)

    fraud_count = sum(labels)
    normal_count = len(labels) - fraud_count
    print(f"原始标签分布: 正常={normal_count}, 欺诈={fraud_count}, 欺诈比例={fraud_count/len(labels):.3f}")

    # 测试不同的alpha值
    alpha_values = [10.0, 1.0, 0.5, 0.1]

    for alpha in alpha_values:
        print(f"\n{'='*60}")
        print(f"测试 α = {alpha}")
        print(f"{'='*60}")

        # 创建Non-IID分割器
        splitter = NonIIDDataSplitter(
            alpha=alpha,
            min_samples_per_client=config['data']['min_samples_per_client'],
            seed=config['global']['seed']
        )

        # 创建Non-IID分割
        client_indices = splitter.create_label_skew_split(
            train_dataset,
            config['federated']['num_clients']
        )

        # 验证分割结果
        total_assigned = sum(len(indices) for indices in client_indices)
        print(f"\n验证结果:")
        print(f"  总分配样本数: {total_assigned}")
        print(f"  原始样本数: {len(train_dataset)}")
        print(f"  分配完整性: {'✓' if total_assigned == len(train_dataset) else '✗'}")

        # 计算异构性指标
        client_fraud_ratios = []
        for client_id, indices in enumerate(client_indices):
            if len(indices) == 0:
                continue
            client_labels = [labels[idx] for idx in indices]
            fraud_ratio = sum(client_labels) / len(client_labels)
            client_fraud_ratios.append(fraud_ratio)

        if client_fraud_ratios:
            mean_ratio = np.mean(client_fraud_ratios)
            std_ratio = np.std(client_fraud_ratios)
            cv = std_ratio / mean_ratio if mean_ratio > 0 else 0

            print(f"\n异构性分析:")
            print(f"  平均欺诈比例: {mean_ratio:.3f}")
            print(f"  标准差: {std_ratio:.3f}")
            print(f"  变异系数: {cv:.3f}")
            print(f"  最小欺诈比例: {min(client_fraud_ratios):.3f}")
            print(f"  最大欺诈比例: {max(client_fraud_ratios):.3f}")

def test_iid_split():
    """测试IID数据分配作为对比"""

    # 加载配置
    with open('config/config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)

    print("\n" + "="*60)
    print("IID数据分配测试（对比）")
    print("="*60)

    # 加载数据集
    train_dataset = MockDataset(config['data']['train_path'])

    # 创建IID分割器
    splitter = NonIIDDataSplitter(seed=config['global']['seed'])
    client_indices = splitter.create_iid_split(
        train_dataset,
        config['federated']['num_clients']
    )

    # 统计标签分布
    labels = []
    for sample in train_dataset.samples:
        label_json = sample['output']
        if isinstance(label_json, str):
            label_json = json.loads(label_json)
        label = int(label_json['is_fraud'])
        labels.append(label)

    # 计算各客户端的欺诈比例
    client_fraud_ratios = []
    for indices in client_indices:
        if len(indices) == 0:
            continue
        client_labels = [labels[idx] for idx in indices]
        fraud_ratio = sum(client_labels) / len(client_labels)
        client_fraud_ratios.append(fraud_ratio)

    if client_fraud_ratios:
        mean_ratio = np.mean(client_fraud_ratios)
        std_ratio = np.std(client_fraud_ratios)
        cv = std_ratio / mean_ratio if mean_ratio > 0 else 0

        print(f"\nIID异构性分析:")
        print(f"  平均欺诈比例: {mean_ratio:.3f}")
        print(f"  标准差: {std_ratio:.3f}")
        print(f"  变异系数: {cv:.3f}")
        print(f"  最小欺诈比例: {min(client_fraud_ratios):.3f}")
        print(f"  最大欺诈比例: {max(client_fraud_ratios):.3f}")

if __name__ == "__main__":
    # 测试Non-IID分配
    test_non_iid_split()
    
    # 测试IID分配作为对比
    test_iid_split()
    
    print("\n" + "="*60)
    print("测试完成！")
    print("="*60)
