# Non-IID数据分配实现文档

## 概述

本文档描述了在Fed-CIC联邦学习框架中实现的Non-IID（非独立同分布）数据分配功能。该功能使用Dirichlet分布来控制数据的异构程度，支持从轻度到重度的不同异构水平。

## 实现特点

### 1. 配置驱动
- 通过`config/config.yaml`中的参数控制数据分布类型
- 支持IID和Non-IID两种模式的无缝切换
- 可调节的异构程度参数α

### 2. 标签分布偏斜
- 使用Dirichlet分布创建标签不平衡
- 不同客户端拥有不同比例的欺诈/正常样本
- 模拟真实世界中的数据异构性

### 3. 质量保证
- 确保每个客户端至少有最少样本数
- 保证数据分配的完整性（无样本丢失）
- 详细的统计信息输出

## 配置参数

在`config/config.yaml`中添加了以下参数：

```yaml
data:
  # 原有参数...
  distribution_type: "non_iid"   # 数据分布类型: "iid" 或 "non_iid"
  non_iid_alpha: 0.5             # Dirichlet分布参数，控制异构程度
  min_samples_per_client: 10     # 每个客户端最少样本数
```

### 参数说明

- **distribution_type**: 
  - `"iid"`: 传统的随机平均分配
  - `"non_iid"`: 使用Dirichlet分布的异构分配

- **non_iid_alpha**: Dirichlet分布参数
  - `α > 10`: 接近IID分布（轻度异构）
  - `α = 1.0`: 中等异构程度
  - `α = 0.5`: 较强异构程度
  - `α = 0.1`: 极强异构程度（接近极端分布）

- **min_samples_per_client**: 每个客户端的最少样本数，防止某些客户端数据过少

## 核心实现

### 1. NonIIDDataSplitter类

位置：`utils/non_iid_splitter.py`

主要方法：
- `create_label_skew_split()`: 创建标签分布偏斜的数据分割
- `create_iid_split()`: 创建IID数据分割（对比用）
- `get_labels_from_dataset()`: 从数据集提取标签
- `_ensure_min_samples()`: 确保最少样本数
- `_print_split_statistics()`: 打印分配统计

### 2. RoundRunner集成

位置：`train_rounds/round_runner.py`

修改内容：
- 在初始化时预先分配数据：`_prepare_data_split()`
- 使用预分配的索引替代动态分配
- 保持验证集分配逻辑不变

## 异构性评估指标

### 1. 变异系数 (Coefficient of Variation)
```
CV = σ(fraud_ratios) / μ(fraud_ratios)
```
- 衡量客户端间欺诈比例的相对离散程度
- 值越大表示异构性越强

### 2. 标准差
- 欺诈比例的绝对离散程度
- 反映数据分布的不均匀性

### 3. 范围
- 最小和最大欺诈比例
- 显示异构性的极端程度

## 实验结果示例

基于24,246个训练样本，30个客户端的测试结果：

| α值 | 变异系数 | 欺诈比例范围 | 异构程度 |
|-----|----------|--------------|----------|
| 10.0 | 0.194 | 0.290-0.669 | 轻度异构 |
| 1.0 | 0.591 | 0.004-0.965 | 中度异构 |
| 0.5 | 0.493 | 0.016-1.000 | 较强异构 |
| 0.1 | 0.620 | 0.000-1.000 | 极强异构 |
| IID | 0.042 | 0.459-0.533 | 几乎同构 |

## 使用方法

### 1. 配置设置
修改`config/config.yaml`：
```yaml
data:
  distribution_type: "non_iid"
  non_iid_alpha: 0.5  # 根据需要调整
```

### 2. 运行实验
```bash
# 测试数据分配
python test_non_iid.py

# 运行完整实验
python experiments/run_exp.py

# 批量测试不同α值
python run_non_iid_experiment.py
```

### 3. 结果分析
- 查看控制台输出的分配统计
- 检查`logs/`目录中的训练日志
- 分析不同α值下的模型性能

## 技术优势

### 1. 真实性
- 模拟真实世界的数据分布不均
- 更好地评估算法的鲁棒性

### 2. 可控性
- 通过α参数精确控制异构程度
- 支持从轻度到极度异构的全范围测试

### 3. 完整性
- 保证数据分配的完整性
- 详细的统计信息便于分析

### 4. 兼容性
- 与现有Fed-CIC框架完全兼容
- 不影响其他功能模块

## 论文实验建议

### 1. 对比实验
在不同α值下比较Fed-CIC与基线算法：
- α = 10.0, 1.0, 0.5, 0.1
- 每个设置运行多次取平均值

### 2. 消融实验
在Non-IID环境下验证各组件的有效性：
- 委员会机制在异构环境下的作用
- 激励机制对数据质量识别的效果

### 3. 性能分析
- 模型性能随异构程度的变化趋势
- 通信效率在异构环境下的保持情况
- 收敛性和稳定性分析

## 注意事项

1. **内存使用**: 极端异构设置可能导致某些客户端数据量很大
2. **训练时间**: 数据不平衡可能影响训练时间
3. **随机种子**: 确保实验可重现性
4. **最少样本数**: 避免客户端数据过少影响训练效果

## 未来扩展

1. **特征分布偏斜**: 除标签偏斜外，还可实现特征分布的异构
2. **动态异构**: 支持训练过程中异构程度的动态变化
3. **多维异构**: 同时考虑标签、特征、数量等多种异构性
4. **自适应分配**: 根据客户端能力自适应调整数据分配
